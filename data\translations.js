// Arquivo de traduções para internacionalização
export const translations = {
  'pt-BR': {
    // Textos gerais
    'loading': 'Carregando...',
    'game_over': 'Game Over! A música era',
    'congratulations': '<PERSON><PERSON><PERSON><PERSON>! Você acertou!',
    'try_again': 'Tente novamente!',
    'skipped': 'Pulado! Próxima dica liberada.',
    'select_from_list': 'Por favor, selecione uma música da lista de sugestões.',

    // Botões e controles
    'play': 'Reproduzir',
    'pause': 'Pausar',
    'skip': 'Pular',
    'guess': 'Adivinhar',
    'song_input_placeholder': 'Digite o nome da música...',

    // Menu
    'menu_how_to_play': 'Como jogar',
    'menu_settings': 'Configurações',
    'menu_error_report': 'Relatório de erro',

    // Como jogar
    'how_to_play_1': '1. Clique play para ouvir um trecho da música.',
    'how_to_play_2': '2. Procure pela música que você acha que o trecho pertence.',
    'how_to_play_3': '3. Clique skip para passar para o próximo trecho.',
    'how_to_play_4': '4. Se você errar, revelaremos um trecho adicional da música para ajudar.',
    'how_to_play_5': '5. Você tem 6 tentativas no total.',

    // Configurações
    'settings_colorblind': 'Modo daltônico',
    'settings_sound': 'Sons',
    'settings_animations': 'Animações',
    'settings_language': 'Idioma',

    // Relatório de erro
    'error_report_title': 'Relatório de erro',
    'error_description': 'Descrição do erro',
    'error_email': 'Seu e-mail (opcional)',
    'error_submit': 'Enviar relatório',
    'error_success': 'Relatório enviado com sucesso! Obrigado pela sua contribuição.',
    'error_placeholder': 'Descreva o problema que você encontrou...',
    'email_placeholder': 'Seu e-mail para contato (opcional)',

    // Footer
    'footer_rights': '© 2025 Ludomusic - Todos os direitos reservados',
    'footer_developed': 'Desenvolvido com ❤️ para a comunidade gamer',
    'footer_background': 'Background feito por',
    'footer_logo': 'Logo feita por',
    'footer_inspired': 'Inspirado por',
    'footer_disclaimer': 'Este site utiliza trechos de áudio com fins de entretenimento. Todos os direitos sobre as músicas pertencem aos seus respectivos detentores.',
    'footer_terms': 'Termos de Uso',
    'footer_privacy': 'Política de Privacidade',
    'footer_removal': 'Remoção de Conteúdo',

    // Multiplayer
    'multiplayer': 'Multiplayer',
    'create_room': 'Criar Sala',
    'join_room': 'Entrar na Sala',
    'room_code': 'Código da Sala',
    'nickname': 'Nome:',
    'nickname_required': 'Nome é obrigatório',
    'room_code_required': 'Código da sala é obrigatório',
    'creating_room': 'Criando sala...',
    'joining_room': 'Entrando na sala...',
    'waiting_for_players': 'Aguardando Jogadores',
    'players_in_room': 'Jogadores na sala',
    'start_game': 'Iniciar Jogo',
    'leave_room': 'Sair da Sala',
    'minimum_players': 'Mínimo 2 jogadores para iniciar',
    'room_not_found': 'Sala não encontrada',
    'round': 'Rodada',
    'score': 'Pontuação',
    'waiting_for_host': 'Aguardando anfitrião iniciar o jogo...',
    'player_guessed_correctly': 'acertou a música!',
    'next_round': 'Próxima Rodada',
    'game_finished': 'Jogo Finalizado!',
    'winner': 'Vencedor',
    'tie': 'Empate!',
    'tiebreaker_round': 'Rodada de Desempate',
    'final_scores': 'Pontuações Finais',
    'play_again': 'Jogar Novamente',
    'back_to_lobby': 'Voltar para Sala',
    'someone_guessed': 'Alguém acertou! Aguarde a próxima rodada...',
    'your_turn': 'Sua vez de tentar!',
    'waiting_for_others': 'Aguardando outros jogadores...',
    'round_of': 'Rodada {current} de {total}',
    'no_one_guessed': 'Ninguém acertou a música!',
    'invite_friends': 'Convidar Amigos',

    // Sharing functionality
    'share_result': 'Compartilhar Resultado',
    'share': 'Compartilhar',
    'share_text_won': 'Acertei a música em {attempts} tentativas no LudoMusic! 🎵',
    'share_text_lost': 'Não consegui acertar esta música no LudoMusic! 🎵',
    'share_url_text': 'Teste seus conhecimentos musicais de videogames:',
    'copy_result': 'Copiar Resultado',
    'result_copied': 'Resultado copiado!',
    'global_stats_message': '{totalPlayers} pessoas já adivinharam / {averageAttempts} tentativas médias',
    'no_stats_yet': 'Seja o primeiro a jogar!',

    // Action buttons
    'report_error': 'Reportar Erro',
    'copy_link': 'Copiar Link',
    'support_project': 'Apoiar',
    'youtube': 'YouTube',

    // Estatísticas
    'statistics': 'Estatísticas',
    'total_games': 'Partidas Jogadas',
    'win_percentage': 'Taxa de Vitória',
    'average_attempts': 'Média de Tentativas',
    'attempt_distribution': 'Distribuição de Acertos',
    'current_game': 'Partida Atual',
    'won_in_attempts': 'Acertou em',
    'lost_game': 'Não conseguiu acertar',
    'continue': 'Continuar',

    // Modo Infinito
    'infinite_mode': 'Modo Infinito',
    'daily_mode': 'Modo Diário',
    'current_streak': 'Sequência Atual',
    'best_record': 'Melhor Recorde',
    'songs_completed': 'Músicas Completadas',
    'infinite_game_over': 'Fim da Sequência!',
    'new_record': 'Novo Recorde!',
    'streak_of': 'Sequência de {count} músicas',
    'play_again_infinite': 'Jogar Novamente',
    'next_song': 'Próxima Música',
    'all_songs_completed': 'Parabéns! Você completou todas as músicas disponíveis!',
    'infinite_statistics': 'Estatísticas do Modo Infinito',

    // Tutorial/Introdução
    'welcome_title': 'Bem-vindo ao LudoMusic!',
    'welcome_subtitle': 'O jogo de adivinhação de músicas de videogames',
    'tutorial_what_is': 'O que é o LudoMusic?',
    'tutorial_what_is_desc': 'LudoMusic é um jogo onde você ouve trechos de músicas de videogames e tenta adivinhar qual é a música. Teste seus conhecimentos musicais dos games!',
    'tutorial_game_modes': 'Modos de Jogo',
    'tutorial_daily_mode': 'Modo Diário',
    'tutorial_daily_desc': 'Uma nova música por dia para todos os jogadores. Você tem 6 tentativas para acertar!',
    'tutorial_infinite_mode': 'Modo Infinito',
    'tutorial_infinite_desc': 'Jogue músicas aleatórias sem parar! Veja quantas você consegue acertar em sequência.',
    'tutorial_multiplayer_mode': 'Modo Multiplayer',
    'tutorial_multiplayer_desc': 'Jogue com amigos em salas privadas! Compete para ver quem acerta mais músicas.',
    'tutorial_multiplayer_beta': '⚠️ Este modo está em BETA e pode apresentar problemas.',
    'tutorial_how_to_play': 'Como Jogar',
    'tutorial_step_1': '1. Clique em "Reproduzir" para ouvir um trecho da música',
    'tutorial_step_2': '2. Digite o nome da música no campo de busca',
    'tutorial_step_3': '3. Selecione a música correta da lista de sugestões',
    'tutorial_step_4': '4. Se errar, você ganha uma dica adicional (trecho mais longo)',
    'tutorial_step_5': '5. Você tem até 6 tentativas para acertar!',
    'tutorial_tips': 'Dicas',
    'tutorial_tip_1': '• Use fones de ouvido para uma melhor experiência',
    'tutorial_tip_2': '• Preste atenção nos detalhes da melodia',
    'tutorial_tip_3': '• As músicas podem ser de qualquer videogame',
    'tutorial_tip_4': '• Você pode pular tentativas se não souber',
    'tutorial_start_playing': 'Começar a Jogar',
    'tutorial_close': 'Fechar Tutorial',
  },
  'en-US': {
    // General texts
    'loading': 'Loading...',
    'game_over': 'Game Over! The song was',
    'congratulations': 'Congratulations! You got it right!',
    'try_again': 'Try again!',
    'skipped': 'Skipped! Next hint unlocked.',
    'select_from_list': 'Please select a song from the suggestions list.',

    // Buttons and controls
    'play': 'Play',
    'pause': 'Pause',
    'skip': 'Skip',
    'guess': 'Guess',
    'song_input_placeholder': 'Type the song name...',

    // Menu
    'menu_how_to_play': 'How to play',
    'menu_settings': 'Settings',
    'menu_error_report': 'Error report',

    // How to play
    'how_to_play_1': '1. Click play to listen to a song snippet.',
    'how_to_play_2': '2. Search for the song you think the snippet belongs to.',
    'how_to_play_3': '3. Click skip to move to the next snippet.',
    'how_to_play_4': '4. If you make a mistake, we\'ll reveal an additional snippet to help.',
    'how_to_play_5': '5. You have 6 attempts in total.',

    // Settings
    'settings_colorblind': 'Colorblind mode',
    'settings_sound': 'Sounds',
    'settings_animations': 'Animations',
    'settings_language': 'Language',

    // Error report
    'error_report_title': 'Error report',
    'error_description': 'Error description',
    'error_email': 'Your email (optional)',
    'error_submit': 'Submit report',
    'error_success': 'Report submitted successfully! Thank you for your contribution.',
    'error_placeholder': 'Describe the issue you encountered...',
    'email_placeholder': 'Your contact email (optional)',

    // Footer
    'footer_rights': '© 2025 Ludomusic - All rights reserved',
    'footer_developed': 'Developed with ❤️ for the gaming community',
    'footer_background': 'Background made by',
    'footer_logo': 'Logo made by',
    'footer_inspired': 'Inspired by',
    'footer_disclaimer': 'This site uses audio snippets for entertainment purposes. All rights to the songs belong to their respective owners.',
    'footer_terms': 'Terms of Use',
    'footer_privacy': 'Privacy Policy',
    'footer_removal': 'Content Removal',

    // Multiplayer
    'multiplayer': 'Multiplayer',
    'create_room': 'Create Room',
    'join_room': 'Join Room',
    'room_code': 'Room Code',
    'nickname': 'Nickname',
    'nickname_required': 'Nickname is required',
    'room_code_required': 'Room code is required',
    'creating_room': 'Creating room...',
    'joining_room': 'Joining room...',
    'waiting_for_players': 'Waiting for players...',
    'players_in_room': 'Players in room',
    'start_game': 'Start Game',
    'leave_room': 'Leave Room',
    'minimum_players': 'Minimum 2 players to start',
    'room_not_found': 'Room not found',
    'round': 'Round',
    'score': 'Score',
    'waiting_for_host': 'Waiting for host to start the game...',
    'player_guessed_correctly': 'guessed correctly!',
    'next_round': 'Next Round',
    'game_finished': 'Game Finished!',
    'winner': 'Winner',
    'tie': 'Tie!',
    'tiebreaker_round': 'Tiebreaker Round',
    'final_scores': 'Final Scores',
    'play_again': 'Play Again',
    'back_to_lobby': 'Back to Lobby',
    'someone_guessed': 'Someone guessed correctly! Wait for next round...',
    'your_turn': 'Your turn to try!',
    'waiting_for_others': 'Waiting for other players...',
    'round_of': 'Round {current} of {total}',
    'no_one_guessed': 'No one guessed the song!',
    'invite_friends': 'Invite Friends',

    // Sharing functionality
    'share_result': 'Share Result',
    'share': 'Share',
    'share_text_won': 'I guessed the song in {attempts} attempts on LudoMusic! 🎵',
    'share_text_lost': 'I couldn\'t guess this song on LudoMusic! 🎵',
    'share_url_text': 'Test your video game music knowledge:',
    'copy_result': 'Copy Result',
    'result_copied': 'Result copied!',
    'global_stats_message': '{totalPlayers} people have already guessed / {averageAttempts} average attempts',
    'no_stats_yet': 'Be the first to play!',

    // Action buttons
    'report_error': 'Report Error',
    'copy_link': 'Copy Link',
    'support_project': 'Support',
    'youtube': 'YouTube',

    // Statistics
    'statistics': 'Statistics',
    'total_games': 'Games Played',
    'win_percentage': 'Win Rate',
    'average_attempts': 'Average Attempts',
    'attempt_distribution': 'Guess Distribution',
    'current_game': 'Current Game',
    'won_in_attempts': 'Won in',
    'lost_game': 'Could not guess',
    'continue': 'Continue',

    // Infinite Mode
    'infinite_mode': 'Infinite Mode',
    'daily_mode': 'Daily Mode',
    'current_streak': 'Current Streak',
    'best_record': 'Best Record',
    'songs_completed': 'Songs Completed',
    'infinite_game_over': 'Streak Ended!',
    'new_record': 'New Record!',
    'streak_of': 'Streak of {count} songs',
    'play_again_infinite': 'Play Again',
    'next_song': 'Next Song',
    'all_songs_completed': 'Congratulations! You completed all available songs!',
    'infinite_statistics': 'Infinite Mode Statistics',

    // Tutorial/Introduction
    'welcome_title': 'Welcome to LudoMusic!',
    'welcome_subtitle': 'The video game music guessing game',
    'tutorial_what_is': 'What is LudoMusic?',
    'tutorial_what_is_desc': 'LudoMusic is a game where you listen to video game music snippets and try to guess the song. Test your gaming music knowledge!',
    'tutorial_game_modes': 'Game Modes',
    'tutorial_daily_mode': 'Daily Mode',
    'tutorial_daily_desc': 'One new song per day for all players. You have 6 attempts to get it right!',
    'tutorial_infinite_mode': 'Infinite Mode',
    'tutorial_infinite_desc': 'Play random songs non-stop! See how many you can get right in a row.',
    'tutorial_multiplayer_mode': 'Multiplayer Mode',
    'tutorial_multiplayer_desc': 'Play with friends in private rooms! Compete to see who can guess more songs.',
    'tutorial_multiplayer_beta': '⚠️ This mode is in BETA and may have issues.',
    'tutorial_how_to_play': 'How to Play',
    'tutorial_step_1': '1. Click "Play" to listen to a music snippet',
    'tutorial_step_2': '2. Type the song name in the search field',
    'tutorial_step_3': '3. Select the correct song from the suggestions list',
    'tutorial_step_4': '4. If you\'re wrong, you get an additional hint (longer snippet)',
    'tutorial_step_5': '5. You have up to 6 attempts to get it right!',
    'tutorial_tips': 'Tips',
    'tutorial_tip_1': '• Use headphones for a better experience',
    'tutorial_tip_2': '• Pay attention to melody details',
    'tutorial_tip_3': '• Songs can be from any video game',
    'tutorial_tip_4': '• You can skip attempts if you don\'t know',
    'tutorial_start_playing': 'Start Playing',
    'tutorial_close': 'Close Tutorial',
  },
  'es': {
    // Textos generales
    'loading': 'Cargando...',
    'game_over': '¡Juego terminado! La canción era',
    'congratulations': '¡Felicidades! ¡Has acertado!',
    'try_again': '¡Inténtalo de nuevo!',
    'skipped': '¡Saltado! Siguiente pista desbloqueada.',
    'select_from_list': 'Por favor, selecciona una canción de la lista de sugerencias.',

    // Botones y controles
    'play': 'Reproducir',
    'pause': 'Pausar',
    'skip': 'Saltar',
    'guess': 'Adivinar',
    'song_input_placeholder': 'Escribe el nombre de la canción...',

    // Menú
    'menu_how_to_play': 'Cómo jugar',
    'menu_settings': 'Configuración',
    'menu_error_report': 'Informe de error',

    // Cómo jugar
    'how_to_play_1': '1. Haz clic en reproducir para escuchar un fragmento de la canción.',
    'how_to_play_2': '2. Busca la canción a la que crees que pertenece el fragmento.',
    'how_to_play_3': '3. Haz clic en saltar para pasar al siguiente fragmento.',
    'how_to_play_4': '4. Si te equivocas, revelaremos un fragmento adicional para ayudarte.',
    'how_to_play_5': '5. Tienes 6 intentos en total.',

    // Configuración
    'settings_colorblind': 'Modo daltónico',
    'settings_sound': 'Sonidos',
    'settings_animations': 'Animaciones',
    'settings_language': 'Idioma',

    // Informe de error
    'error_report_title': 'Informe de error',
    'error_description': 'Descripción del error',
    'error_email': 'Tu correo electrónico (opcional)',
    'error_submit': 'Enviar informe',
    'error_success': '¡Informe enviado con éxito! Gracias por tu contribución.',
    'error_placeholder': 'Describe el problema que encontraste...',
    'email_placeholder': 'Tu correo electrónico de contacto (opcional)',

    // Pie de página
    'footer_rights': '© 2025 Ludomusic - Todos los derechos reservados',
    'footer_developed': 'Desarrollado con ❤️ para la comunidad gamer',
    'footer_background': 'Fondo hecho por',
    'footer_logo': 'Logo hecho por',
    'footer_inspired': 'Inspirado por',
    'footer_disclaimer': 'Este sitio utiliza fragmentos de audio con fines de entretenimiento. Todos los derechos de las canciones pertenecen a sus respectivos propietarios.',
    'footer_terms': 'Términos de Uso',
    'footer_privacy': 'Política de Privacidad',
    'footer_removal': 'Eliminación de Contenido',

    // Multiplayer
    'multiplayer': 'Multijugador',
    'create_room': 'Crear Sala',
    'join_room': 'Unirse a Sala',
    'room_code': 'Código de Sala',
    'nickname': 'Apodo',
    'nickname_required': 'El apodo es obligatorio',
    'room_code_required': 'El código de sala es obligatorio',
    'creating_room': 'Creando sala...',
    'joining_room': 'Uniéndose a la sala...',
    'waiting_for_players': 'Esperando jugadores...',
    'players_in_room': 'Jugadores en la sala',
    'start_game': 'Iniciar Juego',
    'leave_room': 'Salir de la Sala',
    'minimum_players': 'Mínimo 2 jugadores para iniciar',
    'room_not_found': 'Sala no encontrada',
    'round': 'Ronda',
    'score': 'Puntuación',
    'waiting_for_host': 'Esperando que el anfitrión inicie el juego...',
    'player_guessed_correctly': '¡adivinó correctamente!',
    'next_round': 'Siguiente Ronda',
    'game_finished': '¡Juego Terminado!',
    'winner': 'Ganador',
    'tie': '¡Empate!',
    'tiebreaker_round': 'Ronda de Desempate',
    'final_scores': 'Puntuaciones Finales',
    'play_again': 'Jugar de Nuevo',
    'back_to_lobby': 'Volver al Lobby',
    'someone_guessed': '¡Alguien adivinó correctamente! Espera la siguiente ronda...',
    'your_turn': '¡Tu turno de intentar!',
    'waiting_for_others': 'Esperando otros jugadores...',
    'round_of': 'Ronda {current} de {total}',
    'no_one_guessed': '¡Nadie adivinó la canción!',
    'invite_friends': 'Invitar Amigos',

    // Funcionalidad de compartir
    'share_result': 'Compartir Resultado',
    'share': 'Compartir',
    'share_text_won': '¡Adiviné la canción en {attempts} intentos en LudoMusic! 🎵',
    'share_text_lost': '¡No pude adivinar esta canción en LudoMusic! 🎵',
    'share_url_text': 'Pon a prueba tus conocimientos musicales de videojuegos:',
    'copy_result': 'Copiar Resultado',
    'result_copied': '¡Resultado copiado!',
    'global_stats_message': '{totalPlayers} personas ya han adivinado / {averageAttempts} intentos promedio',
    'no_stats_yet': '¡Sé el primero en jugar!',

    // Botones de acción
    'report_error': 'Reportar Error',
    'copy_link': 'Copiar Enlace',
    'support_project': 'Apoyar',
    'youtube': 'YouTube',

    // Estadísticas
    'statistics': 'Estadísticas',
    'total_games': 'Partidas Jugadas',
    'win_percentage': 'Tasa de Victoria',
    'average_attempts': 'Promedio de Intentos',
    'attempt_distribution': 'Distribución de Aciertos',
    'current_game': 'Partida Actual',
    'won_in_attempts': 'Ganó en',
    'lost_game': 'No pudo adivinar',
    'continue': 'Continuar',

    // Modo Infinito
    'infinite_mode': 'Modo Infinito',
    'daily_mode': 'Modo Diario',
    'current_streak': 'Racha Actual',
    'best_record': 'Mejor Récord',
    'songs_completed': 'Canciones Completadas',
    'infinite_game_over': '¡Fin de la Racha!',
    'new_record': '¡Nuevo Récord!',
    'streak_of': 'Racha de {count} canciones',
    'play_again_infinite': 'Jugar de Nuevo',
    'next_song': 'Siguiente Canción',
    'all_songs_completed': '¡Felicidades! ¡Completaste todas las canciones disponibles!',
    'infinite_statistics': 'Estadísticas del Modo Infinito',

    // Tutorial/Introducción
    'welcome_title': '¡Bienvenido a LudoMusic!',
    'welcome_subtitle': 'El juego de adivinanza de música de videojuegos',
    'tutorial_what_is': '¿Qué es LudoMusic?',
    'tutorial_what_is_desc': 'LudoMusic es un juego donde escuchas fragmentos de música de videojuegos e intentas adivinar la canción. ¡Pon a prueba tus conocimientos musicales de los juegos!',
    'tutorial_game_modes': 'Modos de Juego',
    'tutorial_daily_mode': 'Modo Diario',
    'tutorial_daily_desc': 'Una nueva canción por día para todos los jugadores. ¡Tienes 6 intentos para acertar!',
    'tutorial_infinite_mode': 'Modo Infinito',
    'tutorial_infinite_desc': '¡Juega canciones aleatorias sin parar! Ve cuántas puedes acertar seguidas.',
    'tutorial_multiplayer_mode': 'Modo Multijugador',
    'tutorial_multiplayer_desc': '¡Juega con amigos en salas privadas! Compite para ver quién puede adivinar más canciones.',
    'tutorial_multiplayer_beta': '⚠️ Este modo está en BETA y puede presentar problemas.',
    'tutorial_how_to_play': 'Cómo Jugar',
    'tutorial_step_1': '1. Haz clic en "Reproducir" para escuchar un fragmento de música',
    'tutorial_step_2': '2. Escribe el nombre de la canción en el campo de búsqueda',
    'tutorial_step_3': '3. Selecciona la canción correcta de la lista de sugerencias',
    'tutorial_step_4': '4. Si te equivocas, obtienes una pista adicional (fragmento más largo)',
    'tutorial_step_5': '5. ¡Tienes hasta 6 intentos para acertar!',
    'tutorial_tips': 'Consejos',
    'tutorial_tip_1': '• Usa auriculares para una mejor experiencia',
    'tutorial_tip_2': '• Presta atención a los detalles de la melodía',
    'tutorial_tip_3': '• Las canciones pueden ser de cualquier videojuego',
    'tutorial_tip_4': '• Puedes saltar intentos si no sabes',
    'tutorial_start_playing': 'Comenzar a Jugar',
    'tutorial_close': 'Cerrar Tutorial',
  }
};

// Função para obter uma tradução
export function getTranslation(key, language = 'pt-BR') {
  if (!translations[language]) {
    language = 'pt-BR'; // Fallback para português
  }

  return translations[language][key] || key;
}
