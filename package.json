{"name": "ludomusic", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "export": "set BUILD_STATIC=true && next build", "build:static": "set BUILD_STATIC=true && next build", "build:hostinger": "npm run export && node scripts/build-for-hostinger.js", "prepare:deploy": "npm run build:hostinger", "generate:sitemap": "node scripts/generate-sitemap.js", "seo:optimize": "npm run generate:sitemap"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@vercel/analytics": "^1.5.0", "@vercel/kv": "^3.0.0", "@vercel/speed-insights": "^1.2.0", "next": "^14.1.0", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0"}}