[{"id": 0, "title": "Beach Buds", "artist": "<PERSON>", "composer": "<PERSON>", "game": "A Short Hike", "context": "Audio - A Short Hike", "audioUrl": "/audio/a-short-hike/01. Beach Buds (Short Hike).mp3", "year": 2019, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 1, "title": "Somewhere in the Woods", "artist": "<PERSON>", "composer": "<PERSON>", "game": "A Short Hike", "context": "Audio - A Short Hike", "audioUrl": "/audio/a-short-hike/02. Somewhere In The Woods (Short Hike).mp3", "year": 2019, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 2, "title": "See You at the Top", "artist": "<PERSON>", "composer": "<PERSON>", "game": "A Short Hike", "context": "Audio - A Short Hike", "audioUrl": "/audio/a-short-hike/03. See You At The Top (Short Hike).mp3", "year": 2019, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 3, "title": "Objection!", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>: Ace <PERSON>", "context": "Audio - Phoenix <PERSON>: Ace <PERSON>", "audioUrl": "/audio/ace-attorney/06. <PERSON> Wright - Objection! 2001.mp3", "year": 2001, "genre": "Visual Novel Soundtrack", "console": "Multi-platform"}, {"id": 4, "title": "Corner the Culprit", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>: Ace <PERSON>", "context": "Audio - Phoenix <PERSON>: Ace <PERSON>", "audioUrl": "/audio/ace-attorney/08. <PERSON> - Corner the Culprit.mp3", "year": 2001, "genre": "Visual Novel Soundtrack", "console": "Multi-platform"}, {"id": 5, "title": "The Truth Revealed", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>: Ace <PERSON>", "context": "Audio - Phoenix <PERSON>: Ace <PERSON>", "audioUrl": "/audio/ace-attorney/09. The Truth Revealed 2001.mp3", "year": 2001, "genre": "Visual Novel Soundtrack", "console": "Multi-platform"}, {"id": 6, "title": "Trial", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>: Ace <PERSON>", "context": "Audio - Phoenix <PERSON>: Ace <PERSON>", "audioUrl": "/audio/ace-attorney/1-04. Ace Attorney - Trial.mp3", "year": 2001, "genre": "Visual Novel Soundtrack", "console": "Multi-platform"}, {"id": 7, "title": "Initial Investigation", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>: Ace <PERSON>", "context": "Audio - Phoenix <PERSON>: Ace <PERSON>", "audioUrl": "/audio/ace-attorney/13. Initial Investigation 2001.mp3", "year": 2001, "genre": "Visual Novel Soundtrack", "console": "Multi-platform"}, {"id": 8, "title": "Turnabout Sisters", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>: Ace <PERSON>", "context": "Audio - Phoenix <PERSON>: Ace <PERSON>", "audioUrl": "/audio/ace-attorney/14. <PERSON> - Turnabout Sisters 2001.mp3", "year": 2001, "genre": "Visual Novel Soundtrack", "console": "Multi-platform"}, {"id": 9, "title": "The Security Guards' Elegy", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>: Ace <PERSON>", "context": "Audio - Phoenix <PERSON>: Ace <PERSON>", "audioUrl": "/audio/ace-attorney/15. Detention Center - The Security Guards' Elegy.mp3", "year": 2001, "genre": "Visual Novel Soundtrack", "console": "Multi-platform"}, {"id": 10, "title": "Godot ~ the Fragrance of Dark Coffee", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>: Ace <PERSON>", "context": "Audio - Phoenix <PERSON>: Ace <PERSON>", "audioUrl": "/audio/ace-attorney/3-11. <PERSON><PERSON> ~ The Fragrance Of Dark Coffee.mp3", "year": 2001, "genre": "Visual Novel Soundtrack", "console": "Multi-platform"}, {"id": 11, "title": "Follow You Into the Dark", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "<PERSON> 2", "context": "Audio - <PERSON> 2", "audioUrl": "/audio/alan-wake-2/01. Follow You Into The Dark.mp3", "year": 2023, "genre": "Horror Game Soundtrack", "console": "Multi-platform"}, {"id": 12, "title": "Wide Awake", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "<PERSON> 2", "context": "Audio - <PERSON> 2", "audioUrl": "/audio/alan-wake-2/02. Wide Awake.mp3", "year": 2023, "genre": "Horror Game Soundtrack", "console": "Multi-platform"}, {"id": 13, "title": "Superhero", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "<PERSON> 2", "context": "Audio - <PERSON> 2", "audioUrl": "/audio/alan-wake-2/03. Superhero.mp3", "year": 2023, "genre": "Horror Game Soundtrack", "console": "Multi-platform"}, {"id": 14, "title": "Animal Crossing Main Theme", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Animal Crossing: New Horizons", "context": "Audio - Animal Crossing: New Horizons", "audioUrl": "/audio/animal-crossing-new-horizons/3-01. Main Theme.mp3", "year": 2020, "genre": "Life Simulation Soundtrack", "console": "Nintendo Switch"}, {"id": 15, "title": "The Story So Far", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Assassin's Creed II", "context": "Audio - Assassin's Creed II", "audioUrl": "/audio/assasins-creed2/001. The Story So Far.mp3", "year": 2009, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 16, "title": "Venice Rooftops", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Assassin's Creed II", "context": "Audio - Assassin's Creed II", "audioUrl": "/audio/assasins-creed2/003. Venice Rooftops.mp3", "year": 2009, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 17, "title": "<PERSON>'s Family", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Assassin's Creed II", "context": "Audio - Assassin's Creed II", "audioUrl": "/audio/assasins-creed2/004. <PERSON>'s Family.mp3", "year": 2009, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 18, "title": "Baldur's Gate Main Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Baldur's Gate 3", "context": "Audio - Baldur's Gate 3", "audioUrl": "/audio/baldurs-gate-3/01. Main Theme.mp3", "year": 2023, "genre": "RPG Soundtrack", "console": "Multi-platform"}, {"id": 19, "title": "Nine Blades", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Baldur's Gate 3", "context": "Audio - Baldur's Gate 3", "audioUrl": "/audio/baldurs-gate-3/05. Nine Blades.mp3", "year": 2023, "genre": "RPG Soundtrack", "console": "Multi-platform"}, {"id": 20, "title": "Weeping Dawn", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Baldur's Gate 3", "context": "Audio - Baldur's Gate 3", "audioUrl": "/audio/baldurs-gate-3/09. Weeping Dawn.mp3", "year": 2023, "genre": "RPG Soundtrack", "console": "Multi-platform"}, {"id": 21, "title": "The Power", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Baldur's Gate 3", "context": "Audio - Baldur's Gate 3", "audioUrl": "/audio/baldurs-gate-3/20. The Power (Choral Version).mp3", "year": 2023, "genre": "RPG Soundtrack", "console": "Multi-platform"}, {"id": 22, "title": "Down by the River", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Baldur's Gate 3", "context": "Audio - Baldur's Gate 3", "audioUrl": "/audio/baldurs-gate-3/39. Down by the River.mp3", "year": 2023, "genre": "RPG Soundtrack", "console": "Multi-platform"}, {"id": 23, "title": "<PERSON>'s Final Act", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Baldur's Gate 3", "context": "Audio - Baldur's Gate 3", "audioUrl": "/audio/baldurs-gate-3/Baldur's Gate 3 - <PERSON>'s Final Act.mp3", "year": 2023, "genre": "RPG Soundtrack", "console": "Multi-platform"}, {"id": 24, "title": "＂I Want to Live＂", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Baldur's Gate 3", "context": "Audio - Baldur's Gate 3", "audioUrl": "/audio/baldurs-gate-3/Ba<PERSON><PERSON>'s Gate 3 - ＂I Want To Live＂.mp3", "year": 2023, "genre": "RPG Soundtrack", "console": "Multi-platform"}, {"id": 25, "title": "Banjo Main Title", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "context": "Audio - Banjo-<PERSON><PERSON><PERSON><PERSON>", "audioUrl": "/audio/banjo-kazooie/banjo-kazooie-1/002 Main Title.mp3", "year": 1998, "genre": "Game Soundtrack", "console": "Nintendo 64"}, {"id": 26, "title": "Spiral Mountain", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "context": "Audio - Banjo-<PERSON><PERSON><PERSON><PERSON>", "audioUrl": "/audio/banjo-kazooie/banjo-kazooie-1/006a Spiral Mountain (All-In-One).mp3", "year": 1998, "genre": "Game Soundtrack", "console": "Nintendo 64"}, {"id": 27, "title": "Mumbo's Mountain", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "context": "Audio - Banjo-<PERSON><PERSON><PERSON><PERSON>", "audioUrl": "/audio/banjo-kazooie/banjo-kazooie-1/011a <PERSON><PERSON>'s Mountain (All-In-One).mp3", "year": 1998, "genre": "Game Soundtrack", "console": "Nintendo 64"}, {"id": 28, "title": "<PERSON><PERSON><PERSON>'s Theme", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "context": "Audio - Banjo-<PERSON><PERSON><PERSON><PERSON>", "audioUrl": "/audio/banjo-kazooie/banjo-kazooie-1/015 <PERSON><PERSON><PERSON>'s Theme.mp3", "year": 1998, "genre": "Game Soundtrack", "console": "Nintendo 64"}, {"id": 29, "title": "Freezeezy Peak", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "context": "Audio - Banjo-<PERSON><PERSON><PERSON><PERSON>", "audioUrl": "/audio/banjo-kazooie/banjo-kazooie-1/033b Free<PERSON>zy Peak (Normal).mp3", "year": 1998, "genre": "Game Soundtrack", "console": "Nintendo 64"}, {"id": 30, "title": "Going Up, Going Down", "artist": "<PERSON>", "composer": "<PERSON>", "game": "BioShock", "context": "Audio - BioShock", "audioUrl": "/audio/bioshock/1-01. Main Title - Going Up, Going Down.mp3", "year": 2007, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 31, "title": "Dancers on a String", "artist": "<PERSON>", "composer": "<PERSON>", "game": "BioShock", "context": "Audio - BioShock", "audioUrl": "/audio/bioshock/1-03. Dancers On A String (Reprise).mp3", "year": 2007, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 32, "title": "You Have to Save My Family", "artist": "<PERSON>", "composer": "<PERSON>", "game": "BioShock", "context": "Audio - BioShock", "audioUrl": "/audio/bioshock/1-04. You Have To Save My Family.mp3", "year": 2007, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 33, "title": "Cave Story", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Cave Story", "context": "Audio - Cave Story", "audioUrl": "/audio/cave-story/1-01 - Cave Story.mp3", "year": 2004, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 34, "title": "Gestation", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Cave Story", "context": "Audio - Cave Story", "audioUrl": "/audio/cave-story/1-03 - Gestation.mp3", "year": 2004, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 35, "title": "Mimiga Town", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Cave Story", "context": "Audio - Cave Story", "audioUrl": "/audio/cave-story/1-05 - Mimiga Town.mp3", "year": 2004, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 36, "title": "Prologue", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Celeste", "context": "Audio - Celeste", "audioUrl": "/audio/Celeste/[Official] <PERSON> Original Soundtrack - 01 - Prologue 1.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 37, "title": "First Steps", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Celeste", "context": "Audio - Celeste", "audioUrl": "/audio/Celeste/[Official] <PERSON> Original Soundtrack - 02 - First Steps.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 38, "title": "Resurrections", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Celeste", "context": "Audio - Celeste", "audioUrl": "/audio/Celeste/[Official] <PERSON> Original Soundtrack - 03 - Resurrections.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 39, "title": "Anxiety", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Celeste", "context": "Audio - Celeste", "audioUrl": "/audio/Celeste/[Official] <PERSON> Original Soundtrack - 10 - Anxiety.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 40, "title": "Quiet and Falling", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Celeste", "context": "Audio - Celeste", "audioUrl": "/audio/Celeste/[Official] <PERSON> Original Soundtrack - 11 - Quiet and Falling.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 41, "title": "Starjump", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Celeste", "context": "Audio - Celeste", "audioUrl": "/audio/Celeste/[Official] <PERSON> Original Soundtrack - 14 - Starjump.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 42, "title": "Reflection", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Celeste", "context": "Audio - Celeste", "audioUrl": "/audio/Celeste/[Official] <PERSON> Original Soundtrack - 15 - Reflection.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 43, "title": "Confronting Myself", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Celeste", "context": "Audio - Celeste", "audioUrl": "/audio/Celeste/[Official] <PERSON> Original Soundtrack - 16 - Confronting Myself.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 44, "title": "Little Goth", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Celeste", "context": "Audio - Celeste", "audioUrl": "/audio/Celeste/[Official] <PERSON> Original Soundtrack - 17 - Little Goth.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 45, "title": "Exhale", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Celeste", "context": "Audio - Celeste", "audioUrl": "/audio/Celeste/[Official] <PERSON> Original Soundtrack - 19 - Exhale.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 46, "title": "Heart of the Mountain", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Celeste", "context": "Audio - Celeste", "audioUrl": "/audio/Celeste/[Official] <PERSON> Original Soundtrack - 20 - Heart of the Mountain.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 47, "title": "My Dearest Friends", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Celeste", "context": "Audio - Celeste", "audioUrl": "/audio/Celeste/[Official] <PERSON> Original Soundtrack - 21 - My Dearest Friends.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 48, "title": "<PERSON>", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> Obscur: Expedition 33", "context": "Audio - Clair Obscur: Expedition 33", "audioUrl": "/audio/clair-obscur/01. Alicia.mp3", "year": 2025, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 49, "title": "Renoir", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> Obscur: Expedition 33", "context": "Audio - Clair Obscur: Expedition 33", "audioUrl": "/audio/clair-obscur/01. Renoir.mp3", "year": 2025, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 50, "title": "<PERSON>", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> Obscur: Expedition 33", "context": "Audio - Clair Obscur: Expedition 33", "audioUrl": "/audio/clair-obscur/02. Clair-Obscur.mp3", "year": 2025, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 51, "title": "Our Drafts Unite", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> Obscur: Expedition 33", "context": "Audio - Clair Obscur: Expedition 33", "audioUrl": "/audio/clair-obscur/02. Our Drafts Unite.mp3", "year": 2025, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 52, "title": "<PERSON> Main Theme", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> Obscur: Expedition 33", "context": "Audio - Clair Obscur: Expedition 33", "audioUrl": "/audio/clair-obscur/<PERSON> Obscur Main Theme.mp3", "year": 2025, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 53, "title": "Lost Voice", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> Obscur: Expedition 33", "context": "Audio - Clair Obscur: Expedition 33", "audioUrl": "/audio/clair-obscur/Lost Voice.mp3", "year": 2025, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 54, "title": "LumièRe", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> Obscur: Expedition 33", "context": "Audio - Clair Obscur: Expedition 33", "audioUrl": "/audio/clair-obscur/Lumière.mp3", "year": 2025, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 55, "title": "<PERSON><PERSON>", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> Obscur: Expedition 33", "context": "Audio - Clair Obscur: Expedition 33", "audioUrl": "/audio/clair-obscur/Maelle.mp3", "year": 2025, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 56, "title": "We Lost", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> Obscur: Expedition 33", "context": "Audio - Clair Obscur: Expedition 33", "audioUrl": "/audio/clair-obscur/We Lost.mp3", "year": 2025, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 57, "title": "Et Ratio Principalis", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Control", "context": "Audio - Control", "audioUrl": "/audio/control/01. Et Ratio Principalis.mp3", "year": 2019, "genre": "Action Game Soundtrack", "console": "Multi-platform"}, {"id": 58, "title": "Counterfeit", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Control", "context": "Audio - Control", "audioUrl": "/audio/control/02. Counterfeit.mp3", "year": 2019, "genre": "Action Game Soundtrack", "console": "Multi-platform"}, {"id": 59, "title": "Portam Ad Inferno", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Control", "context": "Audio - Control", "audioUrl": "/audio/control/03. Portam Ad Inferno.mp3", "year": 2019, "genre": "Action Game Soundtrack", "console": "Multi-platform"}, {"id": 60, "title": "Crash Main Menu", "internalTitle": "Crash Team Racing", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Team Racing", "context": "Audio - Crash Team Racing", "audioUrl": "/audio/crash/crash-team-racing/4-02. Main Menu.mp3", "year": 1999, "genre": "Racing Game Soundtrack", "console": "PlayStation"}, {"id": 61, "title": "Crash Cove", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Team Racing", "context": "Audio - Crash Team Racing", "audioUrl": "/audio/crash/crash-team-racing/4-07. Crash Cove.mp3", "year": 1999, "genre": "Racing Game Soundtrack", "console": "PlayStation"}, {"id": 62, "title": "Mystery Caves", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Team Racing", "context": "Audio - Crash Team Racing", "audioUrl": "/audio/crash/crash-team-racing/4-17. Mystery Caves.mp3", "year": 1999, "genre": "Racing Game Soundtrack", "console": "PlayStation"}, {"id": 63, "title": "Polar Pass", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Team Racing", "context": "Audio - Crash Team Racing", "audioUrl": "/audio/crash/crash-team-racing/4-31. Polar Pass.mp3", "year": 1999, "genre": "Racing Game Soundtrack", "console": "PlayStation"}, {"id": 64, "title": "N. Sanity Beach", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Bandicoot", "context": "Audio - Crash Bandicoot", "audioUrl": "/audio/crash/crash1/1-01. N. Sanity Beach.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "PlayStation"}, {"id": 65, "title": "Hog Wild", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Bandicoot", "context": "Audio - Crash Bandicoot", "audioUrl": "/audio/crash/crash1/1-11. Hog Wild.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "PlayStation"}, {"id": 66, "title": "Cortex Power", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Bandicoot", "context": "Audio - Crash Bandicoot", "audioUrl": "/audio/crash/crash1/1-19. Cortex Power.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "PlayStation"}, {"id": 67, "title": "Turtle Woods", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Bandicoot", "context": "Audio - Crash Bandicoot", "audioUrl": "/audio/crash/crash2/2-02. Turtle Woods.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "PlayStation"}, {"id": 68, "title": "Snow Go", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Bandicoot", "context": "Audio - Crash Bandicoot", "audioUrl": "/audio/crash/crash2/2-07. Snow Go.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "PlayStation"}, {"id": 69, "title": "Road to Ruin", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Bandicoot", "context": "Audio - Crash Bandicoot", "audioUrl": "/audio/crash/crash2/2-21. Road to Ruin.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "PlayStation"}, {"id": 70, "title": "Warp Room", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Bandicoot", "context": "Audio - Crash Bandicoot", "audioUrl": "/audio/crash/crash3/3-02. Warp Room.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "PlayStation"}, {"id": 71, "title": "Toad Village", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Bandicoot", "context": "Audio - Crash Bandicoot", "audioUrl": "/audio/crash/crash3/3-03. Toad Village.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "PlayStation"}, {"id": 72, "title": "Future Frenzy", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Crash Bandicoot", "context": "Audio - Crash Bandicoot", "audioUrl": "/audio/crash/crash3/3-21. Future Frenzy.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "PlayStation"}, {"id": 73, "title": "Praise the Lamb", "artist": "River Boy", "composer": "River Boy", "game": "Cult of the Lamb", "context": "Audio - Cult of the Lamb", "audioUrl": "/audio/cult-of-the-lamb/01. Praise the Lamb.mp3", "year": 2022, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 74, "title": "Start a Cult", "artist": "River Boy", "composer": "River Boy", "game": "Cult of the Lamb", "context": "Audio - Cult of the Lamb", "audioUrl": "/audio/cult-of-the-lamb/02. Start a Cult.mp3", "year": 2022, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 75, "title": "Work and Worship", "artist": "River Boy", "composer": "River Boy", "game": "Cult of the Lamb", "context": "Audio - Cult of the Lamb", "audioUrl": "/audio/cult-of-the-lamb/04. Work and Worship.mp3", "year": 2022, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 76, "title": "Darkwood", "artist": "River Boy", "composer": "River Boy", "game": "Cult of the Lamb", "context": "Audio - Cult of the Lamb", "audioUrl": "/audio/cult-of-the-lamb/08. Darkwood.mp3", "year": 2022, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 77, "title": "The One Who Waits", "artist": "River Boy", "composer": "River Boy", "game": "Cult of the Lamb", "context": "Audio - Cult of the Lamb", "audioUrl": "/audio/cult-of-the-lamb/35. The One Who Waits.mp3", "year": 2022, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 78, "title": "Tutorial", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Cuphead", "context": "Audio - Cuphead", "audioUrl": "/audio/cuphead/1-08. Tutorial.mp3", "year": 2017, "genre": "Jazz", "console": "Multi-platform"}, {"id": 79, "title": "Inkwell Isle One", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Cuphead", "context": "Audio - Cuphead", "audioUrl": "/audio/cuphead/1-09. Inkwell Isle One.mp3", "year": 2017, "genre": "Jazz", "console": "Multi-platform"}, {"id": 80, "title": "Botanic Panic", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Cuphead", "context": "Audio - Cuphead", "audioUrl": "/audio/cuphead/1-11. Botanic Panic.mp3", "year": 2017, "genre": "Jazz", "console": "Multi-platform"}, {"id": 81, "title": "Forest Follies", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Cuphead", "context": "Audio - Cuphead", "audioUrl": "/audio/cuphead/1-16. Forest Follies.mp3", "year": 2017, "genre": "Jazz", "console": "Multi-platform"}, {"id": 82, "title": "Floral <PERSON>", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Cuphead", "context": "Audio - Cuphead", "audioUrl": "/audio/cuphead/1-19. Floral Fury.mp3", "year": 2017, "genre": "Jazz", "console": "Multi-platform"}, {"id": 83, "title": "Extraction Action", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Cyberpunk 2077", "context": "Audio - Cyberpunk 2077", "audioUrl": "/audio/cyberpunk/1-02 Extraction Action.mp3", "year": 2020, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 84, "title": "The Rebel Path", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Cyberpunk 2077", "context": "Audio - Cyberpunk 2077", "audioUrl": "/audio/cyberpunk/1-03 The Rebel Path.mp3", "year": 2020, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 85, "title": "The Streets Are Long Ass Gutters", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Cyberpunk 2077", "context": "Audio - Cyberpunk 2077", "audioUrl": "/audio/cyberpunk/1-04 The Streets Are Long-Ass Gutters.mp3", "year": 2020, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 86, "title": "Dark Souls Main Theme", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Dark Souls III", "context": "Audio - Dark Souls 3", "audioUrl": "/audio/dark-souls/1-01 Dark Souls III.mp3", "year": 2016, "genre": "Action RPG Soundtrack", "console": "Multi-platform"}, {"id": 87, "title": "<PERSON><PERSON>, Lord of Cinder", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Dark Souls III", "context": "Audio - Dark Souls 3", "audioUrl": "/audio/dark-souls/22. <PERSON><PERSON>, Lord of Cinder.mp3", "year": 2016, "genre": "Action RPG Soundtrack", "console": "Multi-platform"}, {"id": 88, "title": "Symphony of the Night", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Castlevania: Symphony of the Night", "context": "Audio - Castlevania: Symphony of the Night", "audioUrl": "/audio/dark-souls-symphony/07. Symphony of the Night.mp3", "year": 1997, "genre": "Orchestral Game Music", "console": "PlayStation"}, {"id": 89, "title": "Dead Cells", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Dead Cells", "context": "Audio - Dead Cells", "audioUrl": "/audio/dead-cells/01. Dead Cells.mp3", "year": 2018, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 90, "title": "Prisoner's Awakening", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Dead Cells", "context": "Audio - Dead Cells", "audioUrl": "/audio/dead-cells/02. Prisoner's Awakening.mp3", "year": 2018, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 91, "title": "Promenade of the Condemned", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Dead Cells", "context": "Audio - Dead Cells", "audioUrl": "/audio/dead-cells/03. Promenade Of The Condemned.mp3", "year": 2018, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 92, "title": "The Merchant", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Dead Cells", "context": "Audio - Dead Cells", "audioUrl": "/audio/dead-cells/04. The Merchant.mp3", "year": 2018, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 93, "title": "Beginning", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/02 - Beginning.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 94, "title": "School", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/03 - School.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 95, "title": "<PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/04 - Susie.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 96, "title": "The Door", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/05 - The Door.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 97, "title": "Lancer", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/09 - Lancer.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 98, "title": "<PERSON><PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/10 - <PERSON><PERSON>.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 99, "title": "Field of Hopes and Dreams", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/13 - Field of Hopes and Dreams.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 100, "title": "Scarlet Forest", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/19 - Scarlet Forest.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 101, "title": "Hip Shop", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/28 - Hip Shop.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 102, "title": "Chaos King", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/30 - <PERSON> King.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 103, "title": "The World Revolving", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/33 - THE WORLD REVOLVING.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 104, "title": "<PERSON><PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/39 - Don_t Forget.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 105, "title": "A Cyber World", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/A CYBER'S WORLD.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 106, "title": "Attack of the Killer Queen", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/Attack of the Killer Queen.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 107, "title": "Big Shot", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/BIG SHOT.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 108, "title": "Now's Your Chance to Be a", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/NOW'S YOUR CHANCE TO BE A.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 109, "title": "<PERSON><PERSON><PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/Spamton.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 110, "title": "You Can Always Come Home", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - <PERSON><PERSON><PERSON>", "audioUrl": "/audio/deltarune/You Can Always Come Home.mp3", "year": 2018, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 111, "title": "And the Heavens Shall Tremble", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - Di<PERSON>lo", "audioUrl": "/audio/diablo/01 - And The Heavens Shall Tremble.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 112, "title": "The Eternal Conflict", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - Di<PERSON>lo", "audioUrl": "/audio/diablo/02 - The Eternal Conflict.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 113, "title": "New Tristram", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - Di<PERSON>lo", "audioUrl": "/audio/diablo/03 - New Tristram.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 114, "title": "Caves", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - Di<PERSON>lo", "audioUrl": "/audio/diablo/Caves.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 115, "title": "Crypt", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - Di<PERSON>lo", "audioUrl": "/audio/diablo/Crypt.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 116, "title": "<PERSON><PERSON><PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - Di<PERSON>lo", "audioUrl": "/audio/diablo/Diablo.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 117, "title": "Title Screen", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country", "context": "Audio - Donkey Kong Country", "audioUrl": "/audio/donkey-kong/donkey-kong-country/01 - Title Screen - Donkey Kong Country - <PERSON>.mp3", "year": 1994, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 118, "title": "Simian Segu", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country", "context": "Audio - Donkey Kong Country", "audioUrl": "/audio/donkey-kong/donkey-kong-country/02 - <PERSON><PERSON> - Donkey Kong Country - <PERSON>.mp3", "year": 1994, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 119, "title": "DK Island Swing", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country", "context": "Audio - Donkey Kong Country", "audioUrl": "/audio/donkey-kong/donkey-kong-country/03 - DK Island Swing - Donkey Kong Country - <PERSON>.mp3", "year": 1994, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 120, "title": "<PERSON><PERSON><PERSON>'s Theme", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country", "context": "Audio - Donkey Kong Country", "audioUrl": "/audio/donkey-kong/donkey-kong-country/04 - <PERSON><PERSON><PERSON>'s Theme - Donkey Kong Country - <PERSON>.mp3", "year": 1994, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 121, "title": "<PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country", "context": "Audio - Donkey Kong Country", "audioUrl": "/audio/donkey-kong/donkey-kong-country/05 - <PERSON> Concert - Donkey Kong Country - <PERSON>.mp3", "year": 1994, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 122, "title": "Bonus Room Blitz", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country", "context": "Audio - Donkey Kong Country", "audioUrl": "/audio/donkey-kong/donkey-kong-country/06 - Bonus Room Blitz - Donkey Kong Country - <PERSON>.mp3", "year": 1994, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 123, "title": "Aquatic Ambiance", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country", "context": "Audio - Donkey Kong Country", "audioUrl": "/audio/donkey-kong/donkey-kong-country/07 - Aquatic Ambiance - Donkey Kong Country - <PERSON>.mp3", "year": 1994, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 124, "title": "<PERSON><PERSON>s", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country 2", "context": "Audio - Donkey Kong Country 2", "audioUrl": "/audio/donkey-kong/donkey-kong-country-2/01 - <PERSON><PERSON> - Donkey Kong Country 2 - <PERSON>.mp3", "year": 1995, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 125, "title": "Welcome to Crocodile Isle", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country 2", "context": "Audio - Donkey Kong Country 2", "audioUrl": "/audio/donkey-kong/donkey-kong-country-2/02 - Welcome to Crocodile Isle - Donkey Kong Country 2 - <PERSON>.mp3", "year": 1995, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 126, "title": "<PERSON><PERSON><PERSON>'s Romp", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country 2", "context": "Audio - Donkey Kong Country 2", "audioUrl": "/audio/donkey-kong/donkey-kong-country-2/03 - <PERSON><PERSON><PERSON>'s Romp - Donkey Kong Country 2 - <PERSON>.mp3", "year": 1995, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 127, "title": "<PERSON><PERSON><PERSON>'s Saga", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country 2", "context": "Audio - Donkey Kong Country 2", "audioUrl": "/audio/donkey-kong/donkey-kong-country-2/04 - <PERSON><PERSON><PERSON>'s Saga - Donkey Kong Country 2 - <PERSON>.mp3", "year": 1995, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 128, "title": "Forest Interlude", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country 2", "context": "Audio - Donkey Kong Country 2", "audioUrl": "/audio/donkey-kong/donkey-kong-country-2/10 - <PERSON> Interlude - Donkey Kong Country 2 - <PERSON>.mp3", "year": 1995, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 129, "title": "Stickerbrush Symphony", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country 2", "context": "Audio - Donkey Kong Country 2", "audioUrl": "/audio/donkey-kong/donkey-kong-country-2/17 - Stickerbrush Symphony - Donkey Kong Country 2 - <PERSON>.mp3", "year": 1995, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 130, "title": "Fanfare", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country 3", "context": "Audio - Donkey Kong Country 3", "audioUrl": "/audio/donkey-kong/donkey-kong-country-3/01 Fanfare.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 131, "title": "Dixie Beat", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country 3", "context": "Audio - Donkey Kong Country 3", "audioUrl": "/audio/donkey-kong/donkey-kong-country-3/02 Dixie Beat.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 132, "title": "Crazy Calypso", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country 3", "context": "Audio - Donkey Kong Country 3", "audioUrl": "/audio/donkey-kong/donkey-kong-country-3/03 Crazy Calypso.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 133, "title": "Stilt Village", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country 3", "context": "Audio - Donkey Kong Country 3", "audioUrl": "/audio/donkey-kong/donkey-kong-country-3/06 Stilt Village.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 134, "title": "Nuts and Bolts", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country 3", "context": "Audio - Donkey Kong Country 3", "audioUrl": "/audio/donkey-kong/donkey-kong-country-3/16 Nuts and Bolts.mp3", "year": 1996, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 135, "title": "Don't Starve <PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Don't Starve", "context": "Audio - Don't Starve", "audioUrl": "/audio/dont-starve/01. Don't Starve Theme.mp3", "year": 2013, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 136, "title": "Work to Be Done", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Don't Starve", "context": "Audio - Don't Starve", "audioUrl": "/audio/dont-starve/03. Work To Be Done.mp3", "year": 2013, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 137, "title": "E.F.S.", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Don't Starve", "context": "Audio - Don't Starve", "audioUrl": "/audio/dont-starve/05. E.F.S..mp3", "year": 2013, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 138, "title": "At Doom's Gate", "artist": "<PERSON>", "composer": "<PERSON>", "game": "DOOM", "context": "Audio - DOOM", "audioUrl": "/audio/doom/02. At Doom's Gate.mp3", "year": 2016, "genre": "Metal", "console": "Multi-platform"}, {"id": 139, "title": "The Only Thing They Fear Is You", "artist": "<PERSON>", "composer": "<PERSON>", "game": "DOOM", "context": "Audio - DOOM", "audioUrl": "/audio/doom/22. The Only Thing They Fear Is You.mp3", "year": 2016, "genre": "Metal", "console": "Multi-platform"}, {"id": 140, "title": "Elden Ring", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Elden Ring", "context": "Audio - Elden Ring", "audioUrl": "/audio/elden-ring/1-01 - Elden Ring.mp3", "year": 2022, "genre": "Action RPG Soundtrack", "console": "Multi-platform"}, {"id": 141, "title": "Opening", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Elden Ring", "context": "Audio - Elden Ring", "audioUrl": "/audio/elden-ring/1-02 -Elden Ring Opening.mp3", "year": 2022, "genre": "Action RPG Soundtrack", "console": "Multi-platform"}, {"id": 142, "title": "Anxiety", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Enigma do Medo", "context": "Audio - Enigma do Medo", "audioUrl": "/audio/enigma-do-medo/Anxiety.mp3", "year": 2024, "genre": "Horror Game Soundtrack", "console": "PC"}, {"id": 143, "title": "Final Battle", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Enigma do Medo", "context": "Audio - Enigma do Medo", "audioUrl": "/audio/enigma-do-medo/Final Battle.mp3", "year": 2024, "genre": "Horror Game Soundtrack", "console": "PC"}, {"id": 144, "title": "<PERSON><PERSON><PERSON>", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Enigma do Medo", "context": "Audio - Enigma do Medo", "audioUrl": "/audio/enigma-do-medo/Goliath.mp3", "year": 2024, "genre": "Horror Game Soundtrack", "console": "PC"}, {"id": 145, "title": "<PERSON><PERSON><PERSON><PERSON> King", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Enigma do Medo", "context": "Audio - Enigma do Medo", "audioUrl": "/audio/enigma-do-medo/Immortal King.mp3", "year": 2024, "genre": "Horror Game Soundtrack", "console": "PC"}, {"id": 146, "title": "<PERSON><PERSON><PERSON>", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Enigma do Medo", "context": "Audio - Enigma do Medo", "audioUrl": "/audio/enigma-do-medo/Memoria Timoris.mp3", "year": 2024, "genre": "Horror Game Soundtrack", "console": "PC"}, {"id": 147, "title": "Ode Ao Medo", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Enigma do Medo", "context": "Audio - Enigma do Medo", "audioUrl": "/audio/enigma-do-medo/<PERSON><PERSON> (feat. <PERSON><PERSON>).mp3", "year": 2024, "genre": "Horror Game Soundtrack", "console": "PC"}, {"id": 148, "title": "Crowdfunding Single", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Enigma do Medo", "context": "Audio - Enigma do Medo", "audioUrl": "/audio/enigma-do-medo/Paranormal Order： Enigma of Fear (Crowdfunding Single - Original Game Soundtrack).mp3", "year": 2024, "genre": "Horror Game Soundtrack", "console": "PC"}, {"id": 149, "title": "<PERSON><PERSON><PERSON>", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Enigma do Medo", "context": "Audio - Enigma do Medo", "audioUrl": "/audio/enigma-do-medo/Sigils.mp3", "year": 2024, "genre": "Horror Game Soundtrack", "console": "PC"}, {"id": 150, "title": "Transcend", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Enigma do Medo", "context": "Audio - Enigma do Medo", "audioUrl": "/audio/enigma-do-medo/Transcend (Mia).mp3", "year": 2024, "genre": "Horror Game Soundtrack", "console": "PC"}, {"id": 151, "title": "Opening Theme", "artist": "<PERSON><PERSON><PERSON> & <PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON> & <PERSON><PERSON>", "game": "F-Zero", "context": "Audio - F-Zero", "audioUrl": "/audio/f-zero/01 Opening Theme.mp3", "year": 1990, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 152, "title": "Big Blue", "artist": "<PERSON><PERSON><PERSON> & <PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON> & <PERSON><PERSON>", "game": "F-Zero", "context": "Audio - F-Zero", "audioUrl": "/audio/f-zero/02 Big Blue.mp3", "year": 1990, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 153, "title": "Mute City", "artist": "<PERSON><PERSON><PERSON> & <PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON> & <PERSON><PERSON>", "game": "F-Zero", "context": "Audio - F-Zero", "audioUrl": "/audio/f-zero/09 Mute City.mp3", "year": 1990, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 154, "title": "Everybody Falls", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Fall Guys", "context": "Audio - Fall Guys", "audioUrl": "/audio/fall-guys/1-01. <PERSON> Falls (Fall Guys Theme).mp3", "year": 2020, "genre": "Party Game Soundtrack", "console": "Multi-platform"}, {"id": 155, "title": "Adventure", "artist": "Disasterpeace", "composer": "Disasterpeace", "game": "FEZ", "context": "Audio - FEZ", "audioUrl": "/audio/fez/01 - Adventure.mp3", "year": 2012, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 156, "title": "Puzzle", "artist": "Disasterpeace", "composer": "Disasterpeace", "game": "FEZ", "context": "Audio - FEZ", "audioUrl": "/audio/fez/02 - Puzzle.mp3", "year": 2012, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 157, "title": "Beyond", "artist": "Disasterpeace", "composer": "Disasterpeace", "game": "FEZ", "context": "Audio - FEZ", "audioUrl": "/audio/fez/03 - Beyond.mp3", "year": 2012, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 158, "title": "Prelude", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Final Fantasy VII", "context": "Audio - Final Fantasy VII", "audioUrl": "/audio/final-fantasy-vii/01. Prelude.mp3", "year": 1997, "genre": "JRPG Soundtrack", "console": "PlayStation"}, {"id": 159, "title": "Opening", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Final Fantasy VII", "context": "Audio - Final Fantasy VII", "audioUrl": "/audio/final-fantasy-vii/02. Opening~Bombing Mission.mp3", "year": 1997, "genre": "JRPG Soundtrack", "console": "PlayStation"}, {"id": 160, "title": "<PERSON><PERSON><PERSON> Reactor", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Final Fantasy VII", "context": "Audio - Final Fantasy VII", "audioUrl": "/audio/final-fantasy-vii/03. <PERSON><PERSON>u Reactor.mp3", "year": 1997, "genre": "JRPG Soundtrack", "console": "PlayStation"}, {"id": 161, "title": "Wish My Life Away", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Finding Paradise", "context": "Audio - Finding Paradise", "audioUrl": "/audio/finding-paradise/Finding Paradise - Wish My Life Away (<PERSON>) (Lyrics below).mp3", "year": 2017, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 162, "title": "Bestest Detectives in the World", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Finding Paradise", "context": "Audio - Finding Paradise", "audioUrl": "/audio/finding-paradise/Finding Paradise OST - Bestest Detectives in the World (Outdoor).mp3", "year": 2017, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 164, "title": "<PERSON>'s Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Finding Paradise", "context": "Audio - Finding Paradise", "audioUrl": "/audio/finding-paradise/Finding Paradise OST - <PERSON>'s Theme.mp3", "year": 2017, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 166, "title": "The Scale Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Finding Paradise", "context": "Audio - Finding Paradise", "audioUrl": "/audio/finding-paradise/Finding Paradise OST - The Scale Theme.mp3", "year": 2017, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 167, "title": "Time Is a Place", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Finding Paradise", "context": "Audio - Finding Paradise", "audioUrl": "/audio/finding-paradise/Finding Paradise OST - Time is a Place.mp3", "year": 2017, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 168, "title": "Title Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Finding Paradise", "context": "Audio - Finding Paradise", "audioUrl": "/audio/finding-paradise/Finding Paradise OST - Title Theme (Short Vers.).mp3", "year": 2017, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 169, "title": "Trailer Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Finding Paradise", "context": "Audio - Finding Paradise", "audioUrl": "/audio/finding-paradise/Finding Paradise OST - Trailer Theme.mp3", "year": 2017, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 170, "title": "Where Are You", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Finding Paradise", "context": "Audio - Finding Paradise", "audioUrl": "/audio/finding-paradise/Finding Paradise OST - Where Are You (Duet Version).mp3", "year": 2017, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 171, "title": "March in the Storm", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Fuga: Melodies of Steel", "context": "Audio - Fuga: Melodies of Steel", "audioUrl": "/audio/fuga/1-04 March in the Storm.mp3", "year": 2021, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 172, "title": "<PERSON>nis <PERSON>", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Fuga: Melodies of Steel", "context": "Audio - Fuga: Melodies of Steel", "audioUrl": "/audio/fuga/1-09 Taranis Base.mp3", "year": 2021, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 173, "title": "Flower on the Trails", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Fuga: Melodies of Steel", "context": "Audio - Fuga: Melodies of Steel", "audioUrl": "/audio/fuga/1-10 Flower on the Trails.mp3", "year": 2021, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 174, "title": "Geometry Dash Main Menu", "internalTitle": "Geometry Dash", "artist": "ForeverBound", "composer": "ForeverBound", "game": "Geometry Dash", "context": "Audio - Geometry Dash", "audioUrl": "/audio/geometry-dash/1-01. Main Menu.mp3", "year": 2013, "genre": "Electronic", "console": "Multi-platform"}, {"id": 175, "title": "Stereo Madness", "artist": "ForeverBound", "composer": "ForeverBound", "game": "Geometry Dash", "context": "Audio - Geometry Dash", "audioUrl": "/audio/geometry-dash/1-02. Stereo Madness.mp3", "year": 2013, "genre": "Electronic", "console": "Multi-platform"}, {"id": 176, "title": "Back on Track", "artist": "ForeverBound", "composer": "ForeverBound", "game": "Geometry Dash", "context": "Audio - Geometry Dash", "audioUrl": "/audio/geometry-dash/1-03. Back On Track.mp3", "year": 2013, "genre": "Electronic", "console": "Multi-platform"}, {"id": 177, "title": "Watch Theme", "artist": "<PERSON>", "composer": "<PERSON>", "game": "GoldenEye 007", "context": "Audio - GoldenEye 007", "audioUrl": "/audio/goldeneye/309 007 Watch Theme.mp3", "year": 1997, "genre": "Game Soundtrack", "console": "Nintendo 64"}, {"id": 178, "title": "GTA 4 Theme", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Grand Theft Auto IV", "context": "Audio - Grand Theft Auto IV", "audioUrl": "/audio/grand-theft-auto/gta4.mp3", "year": 2008, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 179, "title": "GTA 5 Theme", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Grand Theft Auto V", "context": "Audio - Grand Theft Auto V", "audioUrl": "/audio/grand-theft-auto/gta5.mp3", "year": 2013, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 180, "title": "GTA San andreas Theme", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Grand Theft Auto San Andreas", "context": "Audio - Grand Theft Auto San Andreas", "audioUrl": "/audio/grand-theft-auto/sanandreas.mp3", "year": 2004, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 181, "title": "GTA Vice City Theme", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Grand Theft Auto Vice City", "context": "Audio - Grand Theft Auto Vice City", "audioUrl": "/audio/grand-theft-auto/vice city.mp3", "year": 2002, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 182, "title": "No Escape", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Hades", "context": "Audio - Hades", "audioUrl": "/audio/hades/01 No Escape.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 183, "title": "The House of Hades", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Hades", "context": "Audio - Hades", "audioUrl": "/audio/hades/02 The House of Hades.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 184, "title": "Out of Tartarus", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Hades", "context": "Audio - Hades", "audioUrl": "/audio/hades/03 Out of Tartarus.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 185, "title": "You're Not Supposed to Be Here", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Half-Life 2", "context": "Audio - Half-Life", "audioUrl": "/audio/half-life/24. You're Not Supposed to Be Here.mp3", "year": 1998, "genre": "FPS Soundtrack", "console": "PC"}, {"id": 186, "title": "Hard Fought", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Half-Life 2", "context": "Audio - Half-Life", "audioUrl": "/audio/half-life/26. Hard Fought.mp3", "year": 1998, "genre": "FPS Soundtrack", "console": "PC"}, {"id": 187, "title": "Enter Hallownest", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Hollow Knight", "context": "Audio - <PERSON>", "audioUrl": "/audio/hollow knight/01. Enter Hallownest.mp3", "year": 2017, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 188, "title": "<PERSON><PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Hollow Knight", "context": "Audio - <PERSON>", "audioUrl": "/audio/hollow knight/06. Hornet.mp3", "year": 2017, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 189, "title": "Reflection", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Hollow Knight", "context": "Audio - <PERSON>", "audioUrl": "/audio/hollow knight/07. Reflection.mp3", "year": 2017, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 190, "title": "City of Tears", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Hollow Knight", "context": "Audio - <PERSON>", "audioUrl": "/audio/hollow knight/09. City of Tears.mp3", "year": 2017, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 191, "title": "Soul Sanctum", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Hollow Knight", "context": "Audio - <PERSON>", "audioUrl": "/audio/hollow knight/14. Soul Sanctum.mp3", "year": 2017, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 192, "title": "The White Lady", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Hollow Knight", "context": "Audio - <PERSON>", "audioUrl": "/audio/hollow knight/17. The White Lady.mp3", "year": 2017, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 193, "title": "Dream", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Hollow Knight", "context": "Audio - <PERSON>", "audioUrl": "/audio/hollow knight/21. Dream.mp3", "year": 2017, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 194, "title": "Hollow Knight", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Hollow Knight", "context": "Audio - <PERSON>", "audioUrl": "/audio/hollow knight/26. <PERSON> Knight.mp3", "year": 2017, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 195, "title": "Horse Steppin", "artist": "M|O|O|N", "composer": "M|O|O|N", "game": "Hotline Miami", "context": "Audio - Hotline Miami", "audioUrl": "/audio/hotline-miami/01. Horse Steppin.mp3", "year": 2012, "genre": "Electronic", "console": "Multi-platform"}, {"id": 196, "title": "Hydrogen", "artist": "M|O|O|N", "composer": "M|O|O|N", "game": "Hotline Miami", "context": "Audio - Hotline Miami", "audioUrl": "/audio/hotline-miami/02. Hydrogen.mp3", "year": 2012, "genre": "Electronic", "console": "Multi-platform"}, {"id": 197, "title": "Paris", "artist": "M|O|O|N", "composer": "M|O|O|N", "game": "Hotline Miami", "context": "Audio - Hotline Miami", "audioUrl": "/audio/hotline-miami/03. Paris.mp3", "year": 2012, "genre": "Electronic", "console": "Multi-platform"}, {"id": 198, "title": "Crystals", "artist": "M|O|O|N", "composer": "M|O|O|N", "game": "Hotline Miami", "context": "Audio - Hotline Miami", "audioUrl": "/audio/hotline-miami/04. Crystals.mp3", "year": 2012, "genre": "Electronic", "console": "Multi-platform"}, {"id": 199, "title": "Vengeance", "artist": "M|O|O|N", "composer": "M|O|O|N", "game": "Hotline Miami", "context": "Audio - Hotline Miami", "audioUrl": "/audio/hotline-miami/05. Vengeance.mp3", "year": 2012, "genre": "Electronic", "console": "Multi-platform"}, {"id": 200, "title": "Let Mom Sleep", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Jet Set Radio", "context": "Audio - Jet Set Radio", "audioUrl": "/audio/jet-set-radio/01 - Let Mom Sleep.mp3", "year": 2000, "genre": "Electronic", "console": "Dreamcast"}, {"id": 201, "title": "Humming the Bassline", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Jet Set Radio", "context": "Audio - Jet Set Radio", "audioUrl": "/audio/jet-set-radio/02 - Humming the Bassline.mp3", "year": 2000, "genre": "Electronic", "console": "Dreamcast"}, {"id": 202, "title": "That's Enough", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Jet Set Radio", "context": "Audio - Jet Set Radio", "audioUrl": "/audio/jet-set-radio/03 - That's Enough.mp3", "year": 2000, "genre": "Electronic", "console": "Dreamcast"}, {"id": 203, "title": "Bossbattle", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>ana <PERSON>", "context": "Audio - Katana <PERSON>", "audioUrl": "/audio/katana-zero/01. Bossbattle.mp3", "year": 2019, "genre": "Electronic", "console": "Multi-platform"}, {"id": 204, "title": "Chinatown", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>ana <PERSON>", "context": "Audio - Katana <PERSON>", "audioUrl": "/audio/katana-zero/05. Chinatown.mp3", "year": 2019, "genre": "Electronic", "console": "Multi-platform"}, {"id": 205, "title": "Katanazero", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>ana <PERSON>", "context": "Audio - Katana <PERSON>", "audioUrl": "/audio/katana-zero/19. Katanazero.mp3", "year": 2019, "genre": "Electronic", "console": "Multi-platform"}, {"id": 206, "title": "Running Through the New World", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Kirby and the Forgoten Land", "context": "Audio - Kirby and the Forgoten Land", "audioUrl": "/audio/kirby/forgotten-land/003. Running Through the New World.mp3", "year": 2022, "genre": "Platform Game Soundtrack", "console": "Nintendo Switch"}, {"id": 207, "title": "Ready to Go!", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Kirby and the Forgoten Land", "context": "Audio - Kirby and the Forgoten Land", "audioUrl": "/audio/kirby/forgotten-land/01. Ready to Go! (Title Screen).mp3", "year": 2022, "genre": "Platform Game Soundtrack", "console": "Nintendo Switch"}, {"id": 208, "title": "Welcome to the New World!", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Kirby and the Forgoten Land", "context": "Audio - Kirby and the Forgoten Land", "audioUrl": "/audio/kirby/forgotten-land/075. Welcome to the New World! (Full).mp3", "year": 2022, "genre": "Platform Game Soundtrack", "console": "Nintendo Switch"}, {"id": 209, "title": "<PERSON>ie Country", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON> Return to Dream Land", "context": "Audio - Return to Dream Land", "audioUrl": "/audio/kirby/return-to-dream-land/1-02. Four Adventurers - <PERSON>ie <PERSON> (Level 1).mp3", "year": 2011, "genre": "Platform Game Soundtrack", "console": "Nintendo Wii"}, {"id": 210, "title": "Magolor, the Far Flung Traveler", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON> Return to Dream Land", "context": "Audio - <PERSON> Return to Dream Land", "audioUrl": "/audio/kirby/return-to-dream-land/1-06. <PERSON><PERSON><PERSON>, the Far-Flung Traveler (Lor Starcutter).mp3", "year": 2011, "genre": "Platform Game Soundtrack", "console": "Nintendo Wii"}, {"id": 211, "title": "Planet Popstar", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON> Return to Dream Land", "context": "Audio - <PERSON> Return to Dream Land", "audioUrl": "/audio/kirby/return-to-dream-land/1-07. Planet Popstar (Planet Popstar Map).mp3", "year": 2011, "genre": "Platform Game Soundtrack", "console": "Nintendo Wii"}, {"id": 212, "title": "<PERSON> Wayfarer", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON> Return to Dream Land", "context": "Audio - <PERSON> Return to Dream Land", "audioUrl": "/audio/kirby/return-to-dream-land/1-19. Woods Wayfarer (Forest Area).mp3", "year": 2011, "genre": "Platform Game Soundtrack", "console": "Nintendo Wii"}, {"id": 213, "title": "Kirby Super Star", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Kirby Super Star", "context": "Audio - Kirby Super Star", "audioUrl": "/audio/kirby/super-star/01 Kirby Super Star.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "SNES"}, {"id": 214, "title": "Green Greens", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Kirby Super Star", "context": "Audio - Kirby Super Star", "audioUrl": "/audio/kirby/super-star/05 Green Greens.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "SNES"}, {"id": 215, "title": "Float Islands", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Kirby Super Star", "context": "Audio - Kirby Super Star", "audioUrl": "/audio/kirby/super-star/06 Float Islands.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "SNES"}, {"id": 216, "title": "Boss Battle", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Kirby Super Star", "context": "Audio - Kirby Super Star", "audioUrl": "/audio/kirby/super-star/08 Boss Battle.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "SNES"}, {"id": 217, "title": "And I Begin to Wonder", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Klonoa: Door to Phantomile", "context": "Audio - Klonoa: Door to Phantomile", "audioUrl": "/audio/klonoa1/1-01. And I Begin to Wonder.mp3", "year": 1997, "genre": "Platform Game Soundtrack", "console": "PlayStation"}, {"id": 218, "title": "Inquisitive Waltz", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Klonoa: Door to Phantomile", "context": "Audio - Klonoa: Door to Phantomile", "audioUrl": "/audio/klonoa1/1-02. Inquisitive Waltz.mp3", "year": 1997, "genre": "Platform Game Soundtrack", "console": "PlayStation"}, {"id": 219, "title": "The Windmill Song", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Klonoa: Door to Phantomile", "context": "Audio - Klonoa: Door to Phantomile", "audioUrl": "/audio/klonoa1/1-04. The Windmill Song.mp3", "year": 1997, "genre": "Platform Game Soundtrack", "console": "PlayStation"}, {"id": 220, "title": "<PERSON><PERSON><PERSON>", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Klonoa: Door to Phantomile", "context": "Audio - Klonoa: Door to Phantomile", "audioUrl": "/audio/klonoa1/2-21. Farewell.mp3", "year": 1997, "genre": "Platform Game Soundtrack", "console": "PlayStation"}, {"id": 221, "title": "The Whisper", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/01. The Whisper.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 222, "title": "Bloody Sunset", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/02. Bloody Sunset.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 223, "title": "Playing in the Sun", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/03. Playing In The Sun.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 224, "title": "Heartbeat from the Last Century", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/04. Heartbeat From The Last Century.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 225, "title": "Trust Them", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/05. Trust Them.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 226, "title": "<PERSON><PERSON><PERSON> of the Dead", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/06. <PERSON><PERSON><PERSON> Of The Dead.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 227, "title": "The Hero", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/07. The Hero.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 228, "title": "The Last Tear", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/08. The Last Tear.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 229, "title": "Visions of Red", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/09. Visions Of Red.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 230, "title": "My Destiny", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/10. My Destiny.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 231, "title": "Heartglaze Hope", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/11. Heartglaze Hope.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 232, "title": "Coming Home", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/12. Coming Home.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 233, "title": "Blue Limbo", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/13. Blue Limbo.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 234, "title": "The End of The Road", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/14. The End Of The Road.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 235, "title": "The Final Hour", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/15. The Final Hour.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 236, "title": "Overthinker", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/16. Overthinker.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 237, "title": "Mother", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/17. Mother.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 238, "title": "Lonely Mountain", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/18. Lonely Mountain.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 239, "title": "Recurring Dream", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/19. Recurring dream.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 240, "title": "Through the Wind", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/20. Through The Wind.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 241, "title": "I'm Not Gone", "artist": "Be<PERSON>li", "composer": "Be<PERSON>li", "game": "Laika: Aged Through Blood", "context": "Audio - Laika: Aged Through Blood", "audioUrl": "/audio/laika-aged-through-blood/21. I'm Not Gone.mp3", "year": 2023, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 242, "title": "Overture", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario Galaxy", "context": "Audio - Super Mario Galaxy", "audioUrl": "/audio/mario/mario galaxy/1-01. Overture.mp3", "year": 2007, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 243, "title": "<PERSON><PERSON> in the Observatory 1", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario Galaxy", "context": "Audio - Super Mario Galaxy", "audioUrl": "/audio/mario/mario galaxy/1-08. <PERSON><PERSON> in the Observatory 1.mp3", "year": 2007, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 244, "title": "Gusty Garden Galaxy", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario Galaxy", "context": "Audio - Super Mario Galaxy", "audioUrl": "/audio/mario/mario galaxy/1-17. Gusty Garden Galaxy.mp3", "year": 2007, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 245, "title": "<PERSON><PERSON> in the Observatory 3", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario Galaxy", "context": "Audio - Super Mario Galaxy", "audioUrl": "/audio/mario/mario galaxy/1-18. <PERSON><PERSON> in the Observatory 3.mp3", "year": 2007, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 246, "title": "Overture Galaxy 2", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario Galaxy 2", "context": "Audio - Super Mario Galaxy 2", "audioUrl": "/audio/mario/mario galaxy 2/1-01. Overture galaxy 2.mp3", "year": 2010, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 247, "title": "Sky Station Galaxy", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario Galaxy 2", "context": "Audio - Super Mario Galaxy 2", "audioUrl": "/audio/mario/mario galaxy 2/1-05. Sky Station Galaxy.mp3", "year": 2010, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 248, "title": "Spin Dig Galaxy", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario Galaxy 2", "context": "Audio - Super Mario Galaxy 2", "audioUrl": "/audio/mario/mario galaxy 2/1-15. Spin-Dig Galaxy.mp3", "year": 2010, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 249, "title": "Flip <PERSON>wap <PERSON>", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario Galaxy 2", "context": "Audio - Super Mario Galaxy 2", "audioUrl": "/audio/mario/mario galaxy 2/1-17. Flip-Swap Galaxy.mp3", "year": 2010, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 250, "title": "Cosmic Cove Galaxy", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario Galaxy 2", "context": "Audio - Super Mario Galaxy 2", "audioUrl": "/audio/mario/mario galaxy 2/1-23. Cosmic Cove Galaxy.mp3", "year": 2010, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 251, "title": "Hightail Falls Galaxy", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario Galaxy 2", "context": "Audio - Super Mario Galaxy 2", "audioUrl": "/audio/mario/mario galaxy 2/1-24. Hightail Falls Galaxy.mp3", "year": 2010, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 252, "title": "Throwback Galaxy", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario Galaxy 2", "context": "Audio - Super Mario Galaxy 2", "audioUrl": "/audio/mario/mario galaxy 2/2-16. Throwback Galaxy.mp3", "year": 2010, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 253, "title": "Fossil Falls", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario Odyssey", "context": "Audio - Super Mario Odyssey", "audioUrl": "/audio/mario/mario odyssey/1-09. Fossil Falls.mp3", "year": 2017, "genre": "Platform Game Soundtrack", "console": "Nintendo Switch"}, {"id": 254, "title": "Steam Gardens", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario Odyssey", "context": "Audio - Super Mario Odyssey", "audioUrl": "/audio/mario/mario odyssey/1-22. Steam Gardens.mp3", "year": 2017, "genre": "Platform Game Soundtrack", "console": "Nintendo Switch"}, {"id": 255, "title": "Jump Up, Super Star!", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario Odyssey", "context": "Audio - Super Mario Odyssey", "audioUrl": "/audio/mario/mario odyssey/2-12. Jump Up, Super Star! - New Donk City Festival.mp3", "year": 2017, "genre": "Platform Game Soundtrack", "console": "Nintendo Switch"}, {"id": 256, "title": "Delfino Plaza", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario Sunshine", "context": "Audio - Super Mario <PERSON>", "audioUrl": "/audio/mario/mario sunshine/10. Delfino Plaza.mp3", "year": 2002, "genre": "Platform Game Soundtrack", "console": "GameCube"}, {"id": 257, "title": "Bianco Hills", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario Sunshine", "context": "Audio - Super Mario <PERSON>", "audioUrl": "/audio/mario/mario sunshine/17. Bianco Hills.mp3", "year": 2002, "genre": "Platform Game Soundtrack", "console": "GameCube"}, {"id": 258, "title": "Ricco Harbor", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario Sunshine", "context": "Audio - Super Mario <PERSON>", "audioUrl": "/audio/mario/mario sunshine/27. Ricco Harbor.mp3", "year": 2002, "genre": "Platform Game Soundtrack", "console": "GameCube"}, {"id": 259, "title": "Yoshi's Island", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario World", "context": "Audio - Super Mario World", "audioUrl": "/audio/mario/mario world/03. Yoshi's Island.mp3", "year": 1990, "genre": "Platform Game Soundtrack", "console": "SNES"}, {"id": 260, "title": "Wandering the Plains", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario World", "context": "Audio - Super Mario World", "audioUrl": "/audio/mario/mario world/04. Wandering the Plains.mp3", "year": 1990, "genre": "Platform Game Soundtrack", "console": "SNES"}, {"id": 261, "title": "Star Road", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario World", "context": "Audio - Super Mario World", "audioUrl": "/audio/mario/mario world/09. Star Road.mp3", "year": 1990, "genre": "Platform Game Soundtrack", "console": "SNES"}, {"id": 262, "title": "Overworld", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario World", "context": "Audio - Super Mario World", "audioUrl": "/audio/mario/mario world/12. Overworld.mp3", "year": 1990, "genre": "Platform Game Soundtrack", "console": "SNES"}, {"id": 263, "title": "Athletic", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario World", "context": "Audio - Super Mario World", "audioUrl": "/audio/mario/mario world/16. Athletic.mp3", "year": 1990, "genre": "Platform Game Soundtrack", "console": "SNES"}, {"id": 264, "title": "Title Screen", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario 3D World", "context": "Audio - Super Mario 3D World", "audioUrl": "/audio/mario/mario-3d-world/1-01. Title Screen.mp3", "year": 2013, "genre": "Platform Game Soundtrack", "console": "Wii U"}, {"id": 265, "title": "World 1", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario 3D World", "context": "Audio - Super Mario 3D World", "audioUrl": "/audio/mario/mario-3d-world/1-02. WORLD 1.mp3", "year": 2013, "genre": "Platform Game Soundtrack", "console": "Wii U"}, {"id": 266, "title": "Super Bell Hill", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario 3D World", "context": "Audio - Super Mario 3D World", "audioUrl": "/audio/mario/mario-3d-world/1-03. Super Bell Hill.mp3", "year": 2013, "genre": "Platform Game Soundtrack", "console": "Wii U"}, {"id": 267, "title": "Chain Link Charge", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario 3D World", "context": "Audio - Super Mario 3D World", "audioUrl": "/audio/mario/mario-3d-world/1-14. Chain-Link Charge.mp3", "year": 2013, "genre": "Platform Game Soundtrack", "console": "Wii U"}, {"id": 268, "title": "Double Cherry Pass", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario 3D World", "context": "Audio - Super Mario 3D World", "audioUrl": "/audio/mario/mario-3d-world/1-28. Double Cherry Pass.mp3", "year": 2013, "genre": "Platform Game Soundtrack", "console": "Wii U"}, {"id": 269, "title": "Snowball Park", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Super Mario 3D World", "context": "Audio - Super Mario 3D World", "audioUrl": "/audio/mario/mario-3d-world/2-07. Snowball Park.mp3", "year": 2013, "genre": "Platform Game Soundtrack", "console": "Wii U"}, {"id": 270, "title": "Mario Ka<PERSON> 64 Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "<PERSON> 64", "context": "Audio - <PERSON> 64", "audioUrl": "/audio/mario/mario-kart-64/01a Mario Kart 64 Theme.mp3", "year": 1996, "genre": "Racing Game Soundtrack", "console": "Nintendo 64"}, {"id": 271, "title": "Wario Stadium", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "<PERSON> 64", "context": "Audio - <PERSON> 64", "audioUrl": "/audio/mario/mario-kart-64/03a 3 Raceways, Wario Stadium.mp3", "year": 1996, "genre": "Racing Game Soundtrack", "console": "Nintendo 64"}, {"id": 272, "title": "Moo Moo Farm", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "<PERSON> 64", "context": "Audio - <PERSON> 64", "audioUrl": "/audio/mario/mario-kart-64/04a Moo Moo Farm, Yoshi Valley.mp3", "year": 1996, "genre": "Racing Game Soundtrack", "console": "Nintendo 64"}, {"id": 273, "title": "Choco Mountain", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "<PERSON> 64", "context": "Audio - <PERSON> 64", "audioUrl": "/audio/mario/mario-kart-64/05a Choco Mountain, Battle Arenas.mp3", "year": 1996, "genre": "Racing Game Soundtrack", "console": "Nintendo 64"}, {"id": 274, "title": "Rainbow Road", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "<PERSON> 64", "context": "Audio - <PERSON> 64", "audioUrl": "/audio/mario/mario-kart-64/18a Rainbow Road.mp3", "year": 1996, "genre": "Racing Game Soundtrack", "console": "Nintendo 64"}, {"id": 275, "title": "<PERSON>", "internalTitle": "<PERSON>", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "<PERSON>", "context": "Audio - <PERSON>", "audioUrl": "/audio/mario/mario-kart-ds/002 Main Menu.mp3", "year": 2005, "genre": "Racing Game Soundtrack", "console": "Nintendo DS"}, {"id": 276, "title": "Singleplayer <PERSON><PERSON>", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "<PERSON>", "context": "Audio - <PERSON>", "audioUrl": "/audio/mario/mario-kart-ds/003 Singleplayer Menu.mp3", "year": 2005, "genre": "Racing Game Soundtrack", "console": "Nintendo DS"}, {"id": 277, "title": "Kart Wii", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Mario <PERSON>", "context": "Audio - Mario <PERSON>", "audioUrl": "/audio/mario/mario-kart-wii/03. <PERSON> Wii.mp3", "year": 2008, "genre": "Racing Game Soundtrack", "console": "Wii"}, {"id": 278, "title": "Mario <PERSON> Wii Main Menu", "internalTitle": "Mario <PERSON>", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Mario <PERSON>", "context": "Audio - Mario <PERSON>", "audioUrl": "/audio/mario/mario-kart-wii/08. Main <PERSON> (Medley).mp3", "year": 2008, "genre": "Racing Game Soundtrack", "console": "Wii"}, {"id": 279, "title": "Luigi Circuit", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Mario <PERSON>", "context": "Audio - Mario <PERSON>", "audioUrl": "/audio/mario/mario-kart-wii/11. Mario Circuit - Luigi Circuit.mp3", "year": 2008, "genre": "Racing Game Soundtrack", "console": "Wii"}, {"id": 280, "title": "<PERSON>o <PERSON>", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Mario <PERSON>", "context": "Audio - Mario <PERSON>", "audioUrl": "/audio/mario/mario-kart-wii/19. Moo Moo Meadows.mp3", "year": 2008, "genre": "Racing Game Soundtrack", "console": "Wii"}, {"id": 281, "title": "Mushroom Gorge", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Mario <PERSON>", "context": "Audio - Mario <PERSON>", "audioUrl": "/audio/mario/mario-kart-wii/21. Mushroom Gorge.mp3", "year": 2008, "genre": "Racing Game Soundtrack", "console": "Wii"}, {"id": 282, "title": "Toad's Factory", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Mario <PERSON>", "context": "Audio - Mario <PERSON>", "audioUrl": "/audio/mario/mario-kart-wii/23. Toad's Factory.mp3", "year": 2008, "genre": "Racing Game Soundtrack", "console": "Wii"}, {"id": 283, "title": "Coconut Mall", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Mario <PERSON>", "context": "Audio - Mario <PERSON>", "audioUrl": "/audio/mario/mario-kart-wii/28. Coconut Mall.mp3", "year": 2008, "genre": "Racing Game Soundtrack", "console": "Wii"}, {"id": 284, "title": "Maple Treeway", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Mario <PERSON>", "context": "Audio - Mario <PERSON>", "audioUrl": "/audio/mario/mario-kart-wii/47. Maple Treeway.mp3", "year": 2008, "genre": "Racing Game Soundtrack", "console": "Wii"}, {"id": 285, "title": "Rainbow Road", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Mario <PERSON>", "context": "Audio - Mario <PERSON>", "audioUrl": "/audio/mario/mario-kart-wii/68. Rainbow Road.mp3", "year": 2008, "genre": "Racing Game Soundtrack", "console": "Wii"}, {"id": 286, "title": "<PERSON>", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>", "context": "Audio - <PERSON>", "audioUrl": "/audio/mario/mario-paint/01a <PERSON>.mp3", "year": 1992, "genre": "Creative Game Soundtrack", "console": "SNES"}, {"id": 287, "title": "Creative Exercise", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>", "context": "Audio - <PERSON>", "audioUrl": "/audio/mario/mario-paint/09 Creative Exercise.mp3", "year": 1992, "genre": "Creative Game Soundtrack", "console": "SNES"}, {"id": 288, "title": "Monkey Song", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>", "context": "Audio - <PERSON>", "audioUrl": "/audio/mario/mario-paint/10 Monkey Song.mp3", "year": 1992, "genre": "Creative Game Soundtrack", "console": "SNES"}, {"id": 289, "title": "Title Screen", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "New Super Mario Bros.", "context": "Audio - New Super Mario Bros.", "audioUrl": "/audio/mario/new-super-mario-bros-ds/01 Title Screen.mp3", "year": 2006, "genre": "Platform Game Soundtrack", "console": "Nintendo DS"}, {"id": 290, "title": "Mushroom Plains", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "New Super Mario Bros.", "context": "Audio - New Super Mario Bros.", "audioUrl": "/audio/mario/new-super-mario-bros-ds/04 World 1 - Mushroom Plains.mp3", "year": 2006, "genre": "Platform Game Soundtrack", "console": "Nintendo DS"}, {"id": 291, "title": "Walking the Plains", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "New Super Mario Bros.", "context": "Audio - New Super Mario Bros.", "audioUrl": "/audio/mario/new-super-mario-bros-ds/05 Walking the Plains.mp3", "year": 2006, "genre": "Platform Game Soundtrack", "console": "Nintendo DS"}, {"id": 292, "title": "Underground Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "New Super Mario Bros.", "context": "Audio - New Super Mario Bros.", "audioUrl": "/audio/mario/new-super-mario-bros-ds/14 Underground Theme.mp3", "year": 2006, "genre": "Platform Game Soundtrack", "console": "Nintendo DS"}, {"id": 293, "title": "Starman", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "New Super Mario Bros.", "context": "Audio - New Super Mario Bros.", "audioUrl": "/audio/mario/new-super-mario-bros-ds/22 Starman.mp3", "year": 2006, "genre": "Platform Game Soundtrack", "console": "Nintendo DS"}, {"id": 294, "title": "Title Screen", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "New Super Mario Bros. Wii", "context": "Audio - New Super Mario Bros. Wii", "audioUrl": "/audio/mario/new-super-mario-bros-wii/02. Title Screen.mp3", "year": 2009, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 295, "title": "World 1 Map", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "New Super Mario Bros. Wii", "context": "Audio - New Super Mario Bros. Wii", "audioUrl": "/audio/mario/new-super-mario-bros-wii/08. World 1 Map.mp3", "year": 2009, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 296, "title": "<PERSON><PERSON>'s Castle", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "New Super Mario Bros. Wii", "context": "Audio - New Super Mario Bros. Wii", "audioUrl": "/audio/mario/new-super-mario-bros-wii/09. <PERSON><PERSON>'s Castle.mp3", "year": 2009, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 297, "title": "Overworld", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "New Super Mario Bros. Wii", "context": "Audio - New Super Mario Bros. Wii", "audioUrl": "/audio/mario/new-super-mario-bros-wii/10. Overworld.mp3", "year": 2009, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 298, "title": "Toad House", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "New Super Mario Bros. Wii", "context": "Audio - New Super Mario Bros. Wii", "audioUrl": "/audio/mario/new-super-mario-bros-wii/15. Toad House.mp3", "year": 2009, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 299, "title": "Tower", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "New Super Mario Bros. Wii", "context": "Audio - New Super Mario Bros. Wii", "audioUrl": "/audio/mario/new-super-mario-bros-wii/24. Tower.mp3", "year": 2009, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 300, "title": "Athletic", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "New Super Mario Bros. Wii", "context": "Audio - New Super Mario Bros. Wii", "audioUrl": "/audio/mario/new-super-mario-bros-wii/31. Athletic.mp3", "year": 2009, "genre": "Platform Game Soundtrack", "console": "Wii"}, {"id": 301, "title": "Paper Mario Main Title", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Paper Mario", "context": "Audio - Paper Mario", "audioUrl": "/audio/mario/paper-mario/102 Main Title.mp3", "year": 2000, "genre": "RPG Soundtrack", "console": "Nintendo 64"}, {"id": 302, "title": "A Party at <PERSON>each's Castle", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Paper Mario", "context": "Audio - Paper Mario", "audioUrl": "/audio/mario/paper-mario/103 A Party at Peach's Castle.mp3", "year": 2000, "genre": "RPG Soundtrack", "console": "Nintendo 64"}, {"id": 303, "title": "<PERSON>'s Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Paper Mario", "context": "Audio - Paper Mario", "audioUrl": "/audio/mario/paper-mario/104 <PERSON>'s Theme.mp3", "year": 2000, "genre": "RPG Soundtrack", "console": "Nintendo 64"}, {"id": 304, "title": "March Ahead", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Paper Mario", "context": "Audio - Paper Mario", "audioUrl": "/audio/mario/paper-mario/105 March Ahead.mp3", "year": 2000, "genre": "RPG Soundtrack", "console": "Nintendo 64"}, {"id": 305, "title": "Battle Fanfare", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Paper Mario", "context": "Audio - Paper Mario", "audioUrl": "/audio/mario/paper-mario/107b Battle Fanfare.mp3", "year": 2000, "genre": "RPG Soundtrack", "console": "Nintendo 64"}, {"id": 306, "title": "Toad Town", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Paper Mario", "context": "Audio - Paper Mario", "audioUrl": "/audio/mario/paper-mario/111 Toad Town.mp3", "year": 2000, "genre": "RPG Soundtrack", "console": "Nintendo 64"}, {"id": 307, "title": "Title Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario 64", "context": "Audio - Super Mario 64", "audioUrl": "/audio/mario/super-mario-64/02 - Title Theme - Super Mario 64 - <PERSON><PERSON>.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "Nintendo 64"}, {"id": 308, "title": "Opening", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario 64", "context": "Audio - Super Mario 64", "audioUrl": "/audio/mario/super-mario-64/04 - Opening - Super Mario 64 - <PERSON><PERSON>.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "Nintendo 64"}, {"id": 309, "title": "Slide<PERSON>", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario 64", "context": "Audio - Super Mario 64", "audioUrl": "/audio/mario/super-mario-64/06 - Slider - Super Mario 64 - <PERSON><PERSON>.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "Nintendo 64"}, {"id": 310, "title": "Inside the Castle Walls", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario 64", "context": "Audio - Super Mario 64", "audioUrl": "/audio/mario/super-mario-64/07 - Inside the Castle Walls - Super Mario 64 - <PERSON><PERSON>.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "Nintendo 64"}, {"id": 311, "title": "Dire, Dire Docks", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario 64", "context": "Audio - Super Mario 64", "audioUrl": "/audio/mario/super-mario-64/09 - <PERSON><PERSON>, <PERSON><PERSON> Docks - Super Mario 64 - <PERSON><PERSON>.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "Nintendo 64"}, {"id": 312, "title": "Lethal Lava Land", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario 64", "context": "Audio - Super Mario 64", "audioUrl": "/audio/mario/super-mario-64/10 - <PERSON><PERSON> Lava Land - Super Mario 64 - <PERSON><PERSON>.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "Nintendo 64"}, {"id": 313, "title": "Snow Mountain", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario 64", "context": "Audio - Super Mario 64", "audioUrl": "/audio/mario/super-mario-64/11 - <PERSON> - Super Mario 64 - <PERSON><PERSON>.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "Nintendo 64"}, {"id": 314, "title": "Haunted House", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario 64", "context": "Audio - Super Mario 64", "audioUrl": "/audio/mario/super-mario-64/12 - Haunted House - Super Mario 64 - <PERSON><PERSON>.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "Nintendo 64"}, {"id": 315, "title": "Merry-Go-Round", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario 64", "context": "Audio - Super Mario 64", "audioUrl": "/audio/mario/super-mario-64/13 - Merry-Go-Round - Super Mario 64 - <PERSON><PERSON>.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "Nintendo 64"}, {"id": 316, "title": "Piranha Plant's Lullaby", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario 64", "context": "Audio - Super Mario 64", "audioUrl": "/audio/mario/super-mario-64/15 - Piranha Plant's Lullaby - Super Mario 64 - <PERSON><PERSON>.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "Nintendo 64"}, {"id": 317, "title": "<PERSON>", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Super Mario 64", "context": "Audio - Super Mario 64", "audioUrl": "/audio/mario/super-mario-64/Bob-omb Battlefield.mp3", "year": 1996, "genre": "Platform Game Soundtrack", "console": "Nintendo 64"}, {"id": 318, "title": "Title Screen", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Mega Man X", "context": "Audio - Mega Man X", "audioUrl": "/audio/mega-man/mega-man-x/Title Screen.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 319, "title": "Intro Stage", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Mega Man X", "context": "Audio - Mega Man X", "audioUrl": "/audio/mega-man/mega-man-x/02 - Intro Stage - Mega Man X - <PERSON><PERSON><PERSON>.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 320, "title": "Armored Armadillo", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Mega Man X", "context": "Audio - Mega Man X", "audioUrl": "/audio/mega-man/mega-man-x/09 - Armored Armadillo - Mega Man X - <PERSON><PERSON><PERSON>.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 321, "title": "Boomer <PERSON>", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Mega Man X", "context": "Audio - Mega Man X", "audioUrl": "/audio/mega-man/mega-man-x/10 - <PERSON><PERSON> - Mega Man X - <PERSON><PERSON><PERSON>.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 322, "title": "<PERSON>ll <PERSON>", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Mega Man X", "context": "Audio - Mega Man X", "audioUrl": "/audio/mega-man/mega-man-x/11 - <PERSON><PERSON> Penguin - Mega Man X - <PERSON><PERSON><PERSON>.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 323, "title": "Flame Mammoth", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Mega Man X", "context": "Audio - Mega Man X", "audioUrl": "/audio/mega-man/mega-man-x/12 - Flame Mammoth - Mega Man X - <PERSON><PERSON><PERSON>.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 324, "title": "Launch Octopus", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Mega Man X", "context": "Audio - Mega Man X", "audioUrl": "/audio/mega-man/mega-man-x/13 - Launch Octopus - Mega Man X - <PERSON><PERSON><PERSON>.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 325, "title": "Spark Mandril", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Mega Man X", "context": "Audio - Mega Man X", "audioUrl": "/audio/mega-man/mega-man-x/14 - <PERSON><PERSON> - Mega Man X - <PERSON><PERSON><PERSON>.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 326, "title": "<PERSON>", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Mega Man X", "context": "Audio - Mega Man X", "audioUrl": "/audio/mega-man/mega-man-x/15 - <PERSON> - Mega Man X - <PERSON><PERSON><PERSON>.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 327, "title": "Storm Eagle", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Mega Man X", "context": "Audio - Mega Man X", "audioUrl": "/audio/mega-man/mega-man-x/16 - <PERSON> - Mega Man X - <PERSON><PERSON><PERSON>.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 328, "title": "Snake Eater", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Metal Gear Solid 3", "context": "Audio - Metal Gear Solid", "audioUrl": "/audio/metal-gear/01 Snake Eater.mp3", "year": 2004, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 329, "title": "Rules of Nature", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Metal Gear Rising: Revengeance", "context": "Audio - Metal Gear Rising: Revengeance", "audioUrl": "/audio/metal-gear-rising/01 Rules of Nature (Platinum Mix).mp3", "year": 2013, "genre": "Action Game Soundtrack", "console": "Multi-platform"}, {"id": 330, "title": "The Only Thing I Know for Real", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Metal Gear Rising: Revengeance", "context": "Audio - Metal Gear Rising: Revengeance", "audioUrl": "/audio/metal-gear-rising/02 The Only Thing I Know For Real (Manic Agenda Mix).mp3", "year": 2013, "genre": "Action Game Soundtrack", "console": "Multi-platform"}, {"id": 331, "title": "Metroid Intro", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Metroid", "context": "Audio - Metroid", "audioUrl": "/audio/metroid/1 - Intro.mp3", "year": 1986, "genre": "Game Soundtrack", "console": "Nintendo"}, {"id": 332, "title": "<PERSON><PERSON>", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Metroid", "context": "Audio - Metroid", "audioUrl": "/audio/metroid/2 - Enter  Samus.mp3", "year": 1986, "genre": "Game Soundtrack", "console": "Nintendo"}, {"id": 333, "title": "Brinstar", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Metroid", "context": "Audio - Metroid", "audioUrl": "/audio/metroid/3 - Brinstar.mp3", "year": 1986, "genre": "Game Soundtrack", "console": "Nintendo"}, {"id": 334, "title": "Subwoofer Lullaby418", "artist": "C418", "composer": "C418", "game": "Minecraft", "context": "Audio - Minecraft", "audioUrl": "/audio/minecraft/03 - Subwoofer Lullaby - Minecraft - C418.mp3", "year": 2011, "genre": "Ambient", "console": "Multi-platform"}, {"id": 335, "title": "Living Mice", "artist": "C418", "composer": "C418", "game": "Minecraft", "context": "Audio - Minecraft", "audioUrl": "/audio/minecraft/05 - <PERSON>ce - Minecraft - C418.mp3", "year": 2011, "genre": "Ambient", "console": "Multi-platform"}, {"id": 336, "title": "Moog City", "artist": "C418", "composer": "C418", "game": "Minecraft", "context": "Audio - Minecraft", "audioUrl": "/audio/minecraft/06 - Moog City - Minecraft - C418.mp3", "year": 2011, "genre": "Ambient", "console": "Multi-platform"}, {"id": 337, "title": "<PERSON><PERSON><PERSON>", "artist": "C418", "composer": "C418", "game": "Minecraft", "context": "Audio - Minecraft", "audioUrl": "/audio/minecraft/07 - <PERSON><PERSON><PERSON> - Minecraft - C418.mp3", "year": 2011, "genre": "Ambient", "console": "Multi-platform"}, {"id": 338, "title": "Minecraft", "artist": "C418", "composer": "C418", "game": "Minecraft", "context": "Audio - Minecraft", "audioUrl": "/audio/minecraft/08 - Minecraft - Minecraft - C418.mp3", "year": 2011, "genre": "Ambient", "console": "Multi-platform"}, {"id": 339, "title": "<PERSON><PERSON> On Venus", "artist": "C418", "composer": "C418", "game": "Minecraft", "context": "Audio - Minecraft", "audioUrl": "/audio/minecraft/11 - <PERSON><PERSON> On Venus - Minecraft - C418.mp3", "year": 2011, "genre": "Ambient", "console": "Multi-platform"}, {"id": 340, "title": "Dry Hands", "artist": "C418", "composer": "C418", "game": "Minecraft", "context": "Audio - Minecraft", "audioUrl": "/audio/minecraft/12 - Dry Hands - Minecraft - C418.mp3", "year": 2011, "genre": "Ambient", "console": "Multi-platform"}, {"id": 341, "title": "Wet Hands", "artist": "C418", "composer": "C418", "game": "Minecraft", "context": "Audio - Minecraft", "audioUrl": "/audio/minecraft/13 - <PERSON> Hands - Minecraft - C418.mp3", "year": 2011, "genre": "Ambient", "console": "Multi-platform"}, {"id": 342, "title": "Sweden", "artist": "C418", "composer": "C418", "game": "Minecraft", "context": "Audio - Minecraft", "audioUrl": "/audio/minecraft/18 - Sweden - Minecraft - C418.mp3", "year": 2011, "genre": "Ambient", "console": "Multi-platform"}, {"id": 343, "title": "Night in the Woods Title", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Night in the Woods", "context": "Audio - Night in the Woods", "audioUrl": "/audio/night-in-the-woods/02 Title Night in the woods.mp3", "year": 2017, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 344, "title": "Die Anywhere Else", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Night in the Woods", "context": "Audio - Night in the Woods", "audioUrl": "/audio/night-in-the-woods/13 Die Anywhere Else.mp3", "year": 2017, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 345, "title": "Possum Springs", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Night in the Woods", "context": "Audio - Night in the Woods", "audioUrl": "/audio/night-in-the-woods/22 Possum Springs.mp3", "year": 2017, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 346, "title": "Weird Autumn", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Night in the Woods", "context": "Audio - Night in the Woods", "audioUrl": "/audio/night-in-the-woods/26 Weird Autumn.mp3", "year": 2017, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 348, "title": "Awakening", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Nine Sols", "context": "Audio - Nine Sols", "audioUrl": "/audio/nine-sols/02. Awakening.mp3", "year": 2024, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 349, "title": "The Puppeteer ~<PERSON><PERSON><PERSON>'s Theme~", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Nine Sols", "context": "Audio - Nine Sols", "audioUrl": "/audio/nine-sols/24. The Puppeteer ~<PERSON><PERSON><PERSON>'s Theme~.mp3", "year": 2024, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 350, "title": "Ballad of Warriors ~<PERSON><PERSON><PERSON><PERSON>'s Theme~", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Nine Sols", "context": "Audio - Nine Sols", "audioUrl": "/audio/nine-sols/39. Ballad of Warriors ~<PERSON><PERSON><PERSON><PERSON>'s Theme~.mp3", "year": 2024, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 351, "title": "Smile at My Cursed Dream ~<PERSON> E<PERSON>eal's Theme~", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Nine Sols", "context": "Audio - Nine Sols", "audioUrl": "/audio/nine-sols/48. Smile at My Cursed Dream ~<PERSON>'s Theme~.mp3", "year": 2024, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 352, "title": "Our Passion ~<PERSON><PERSON> & <PERSON><PERSON><PERSON>'s Theme~", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Nine Sols", "context": "Audio - Nine Sols", "audioUrl": "/audio/nine-sols/55. Our Passion ~<PERSON><PERSON> & <PERSON><PERSON><PERSON>'s Theme~.mp3", "year": 2024, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 353, "title": "Long Awaited Death ~Ji's Theme~", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Nine Sols", "context": "Audio - Nine Sols", "audioUrl": "/audio/nine-sols/60. <PERSON> Awaited Death ~<PERSON>'s Theme~.mp3", "year": 2024, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 354, "title": "My Will Is Our Truth ~<PERSON>igong's Theme~", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Nine Sols", "context": "Audio - Nine Sols", "audioUrl": "/audio/nine-sols/65. My Will Is Our Truth ~Eigong's Theme~.mp3", "year": 2024, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 355, "title": "Collage", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Nine Sols", "context": "Audio - Nine Sols", "audioUrl": "/audio/nine-sols/Collage.mp3", "year": 2024, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 356, "title": "Title", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - Ō<PERSON><PERSON>", "audioUrl": "/audio/okami/101 - Okami Title.mp3", "year": 2006, "genre": "Adventure Game Soundtrack", "console": "Multi-platform"}, {"id": 357, "title": "Okami Prologue", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - Ō<PERSON><PERSON>", "audioUrl": "/audio/okami/104 - Okami Prologue.mp3", "year": 2006, "genre": "Adventure Game Soundtrack", "console": "Multi-platform"}, {"id": 358, "title": "<PERSON><PERSON>'s Theme", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - Ō<PERSON><PERSON>", "audioUrl": "/audio/okami/108 - <PERSON><PERSON>'s Theme.mp3", "year": 2006, "genre": "Adventure Game Soundtrack", "console": "Multi-platform"}, {"id": 359, "title": "Title", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/01. Title Omori.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 360, "title": "Take a Load Off", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/52. Take A Load Off.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 361, "title": "I Prefer My Pizza 90% Grease", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/53. I Prefer My Pizza 90% Grease.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 362, "title": "Fresh...Ish...", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/54. Fresh...ish....mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 363, "title": "High Fructose Headache", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/55. High Fructose Headache.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 364, "title": "I Think My Dad Shops Here...", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/56. I Think My Dad Shops Here....mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 365, "title": "Just Leave M.E. Alone", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/57. Just Leave Me Alone.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 366, "title": "Cram It Wad", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/58. <PERSON>ram It Wad.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 367, "title": "Respite", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/59. Respite.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 368, "title": "It Means Everything", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/60. It Means Everything..mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 369, "title": "See You Tomorrow", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/61. See You Tomorrow.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 370, "title": "A Home for Flowers", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/62. A Home For Flowers (Empty).mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 371, "title": "Help M.E.", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/63. Help Me.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 372, "title": "Arachnophobia", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/64. Arachnophobia.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 373, "title": "<PERSON><PERSON><PERSON> Steady Go", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/OMORI OST - BREADY STEADY GO.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 374, "title": "By Your Side", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/OMORI OST - By Your Side..mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 375, "title": "Duet", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/OMORI OST - DUET.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 376, "title": "Friends", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/OMORI OST - Friends..mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 377, "title": "Goldenvengeance", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/OMORI OST - GOLDENVENGEANCE.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 378, "title": "<PERSON><PERSON><PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/OMORI OST - OMORI.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 379, "title": "See You Tomorrow", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/OMORI OST - See You Tomorrow.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 380, "title": "<PERSON><PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/OMORI OST - Tee-hee Time.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 381, "title": "Three Bar Logos", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/OMORI OST - Three Bar Logos.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 383, "title": "Tussle Among Trees", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/OMORI OST - Tussle Among Trees.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 384, "title": "World's End Valentine", "artist": "<PERSON>", "composer": "<PERSON>", "game": "OMORI", "context": "Audio - OMORI", "audioUrl": "/audio/omori/OMORI OST - World's End Valentine.mp3", "year": 2020, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 385, "title": "My Burden Is Light", "artist": "Nightmargin", "composer": "Nightmargin", "game": "OneShot", "context": "Audio - OneShot", "audioUrl": "/audio/one-shot/01 My Burden Is Light.mp3", "year": 2016, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 386, "title": "Someplace I Know", "artist": "Nightmargin", "composer": "Nightmargin", "game": "OneShot", "context": "Audio - OneShot", "audioUrl": "/audio/one-shot/02 Someplace I Know.mp3", "year": 2016, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 387, "title": "The Prophecy", "artist": "Nightmargin", "composer": "Nightmargin", "game": "OneShot", "context": "Audio - OneShot", "audioUrl": "/audio/one-shot/05 The Prophecy.mp3", "year": 2016, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 388, "title": "It's Time to Fight Crime", "artist": "Nightmargin", "composer": "Nightmargin", "game": "OneShot", "context": "Audio - OneShot", "audioUrl": "/audio/one-shot/42 IT'S TIME TO FIGHT CRIME.mp3", "year": 2016, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 389, "title": "Ori Main Theme", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Ori and the Blind Forest", "context": "Audio - Ori and the Blind Forest", "audioUrl": "/audio/ori/01 Main Theme.mp3", "year": 2015, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 390, "title": "Lost in the Storm", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Ori and the Blind Forest", "context": "Audio - Ori and the Blind Forest", "audioUrl": "/audio/ori/01. <PERSON><PERSON>, Lost in the Storm (feat. <PERSON><PERSON><PERSON>).mp3", "year": 2015, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 391, "title": "Embracing the Light", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Ori and the Blind Forest", "context": "Audio - Ori and the Blind Forest", "audioUrl": "/audio/ori/02. <PERSON><PERSON>, Embracing the Light (feat. <PERSON>).mp3", "year": 2015, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 392, "title": "A Yearning for the Sky", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Ori and the Blind Forest", "context": "Audio - Ori and the Blind Forest", "audioUrl": "/audio/ori/03 A Yearning for the Sky.mp3", "year": 2015, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 393, "title": "Outer Wilds", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Outer Wilds", "context": "Audio - Outer Wilds", "audioUrl": "/audio/outer wilds/02. Outer Wilds.mp3", "year": 2019, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 394, "title": "Outer Wilds Main Title", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Outer Wilds", "context": "Audio - Outer Wilds", "audioUrl": "/audio/outer wilds/07. Main Title.mp3", "year": 2019, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 395, "title": "End Times", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Outer Wilds", "context": "Audio - Outer Wilds", "audioUrl": "/audio/outer wilds/10. End Times.mp3", "year": 2019, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 396, "title": "Travelers", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Outer Wilds", "context": "Audio - Outer Wilds", "audioUrl": "/audio/outer wilds/20. Travelers.mp3", "year": 2019, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 397, "title": "Billion Years", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Outer Wilds", "context": "Audio - Outer Wilds", "audioUrl": "/audio/outer wilds/22. 14.3 Billion Years.mp3", "year": 2019, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 398, "title": "Mass Destruction Reload", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Persona 3 Reloaded", "context": "Audio - Persona 3 Reloaded", "audioUrl": "/audio/persona/05. <PERSON> Destruction -Reload-.mp3", "year": 2024, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 399, "title": "Last Surprise", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Persona 5", "context": "Audio - Persona 5", "audioUrl": "/audio/persona/1-17. Last Surprise.mp3", "year": 2016, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 400, "title": "<PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Plants vs. Zombies", "context": "Audio - Plants vs. Zombies", "audioUrl": "/audio/plants-vs-zombies/02. <PERSON> (Intro Theme).mp3", "year": 2009, "genre": "Tower Defense Soundtrack", "console": "Multi-platform"}, {"id": 401, "title": "Zombies on Your Lawn", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Plants vs. Zombies", "context": "Audio - Plants vs. Zombies", "audioUrl": "/audio/plants-vs-zombies/15. Zombies on Your Lawn.mp3", "year": 2009, "genre": "Tower Defense Soundtrack", "console": "Multi-platform"}, {"id": 402, "title": "Pokémon Black White Title", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon Black White", "context": "Audio - Pokémon Black White", "audioUrl": "/audio/pokemon/pokemon-black-white/103 - Title.mp3", "year": 2010, "genre": "Game Soundtrack", "console": "Nintendo DS"}, {"id": 403, "title": "Kanoko Town", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon Black White", "context": "Audio - Pokémon Black White", "audioUrl": "/audio/pokemon/pokemon-black-white/106 - Kanoko Town.mp3", "year": 2010, "genre": "Game Soundtrack", "console": "Nintendo DS"}, {"id": 404, "title": "Pokémon Diamond/Pearl Opening Demo", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Pokémon Diamond/Pearl", "context": "Audio - Pokémon Diamond/Pearl", "audioUrl": "/audio/pokemon/pokemon-diamond-pearl/101a Opening Demo (Part 1).mp3", "year": 2006, "genre": "JRPG Soundtrack", "console": "Nintendo DS"}, {"id": 405, "title": "Twinleaf Town", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Pokémon Diamond/Pearl", "context": "Audio - Pokémon Diamond/Pearl", "audioUrl": "/audio/pokemon/pokemon-diamond-pearl/104 Twinleaf Town (Daytime).mp3", "year": 2006, "genre": "JRPG Soundtrack", "console": "Nintendo DS"}, {"id": 406, "title": "Route 201", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Pokémon Diamond/Pearl", "context": "Audio - Pokémon Diamond/Pearl", "audioUrl": "/audio/pokemon/pokemon-diamond-pearl/106 Route 201 (Daytime).mp3", "year": 2006, "genre": "JRPG Soundtrack", "console": "Nintendo DS"}, {"id": 407, "title": "Sandgem Town", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Pokémon Diamond/Pearl", "context": "Audio - Pokémon Diamond/Pearl", "audioUrl": "/audio/pokemon/pokemon-diamond-pearl/113 Sandgem Town (Daytime).mp3", "year": 2006, "genre": "JRPG Soundtrack", "console": "Nintendo DS"}, {"id": 408, "title": "Pokémon Heartgold Soulsilver Opening Demo", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon Heartgold Soulsilver", "context": "Audio - Pokémon Heartgold Soulsilver", "audioUrl": "/audio/pokemon/pokemon-heartgold-soulsilver/101 Opening Demo.mp3", "year": 2009, "genre": "Game Soundtrack", "console": "Nintendo DS"}, {"id": 409, "title": "Pokémon Heartgold Soulsilver Title", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon Heartgold Soulsilver", "context": "Audio - Pokémon Heartgold Soulsilver", "audioUrl": "/audio/pokemon/pokemon-heartgold-soulsilver/102 Title.mp3", "year": 2009, "genre": "Game Soundtrack", "console": "Nintendo DS"}, {"id": 410, "title": "Wakaba Town", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon Heartgold Soulsilver", "context": "Audio - Pokémon Heartgold Soulsilver", "audioUrl": "/audio/pokemon/pokemon-heartgold-soulsilver/104 Wakaba Town.mp3", "year": 2009, "genre": "Game Soundtrack", "console": "Nintendo DS"}, {"id": 411, "title": "Pokémon Fire Red Leaf Green Opening", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon Fire Red Leaf Green", "context": "Audio - Pokémon", "audioUrl": "/audio/pokemon/pokemon-leafgreen-firered/03 ~Opening~.mp3", "year": 2004, "genre": "Game Soundtrack", "console": "Game Boy Advance"}, {"id": 412, "title": "Pallet Town", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon Fire Red Leaf Green", "context": "Audio - Pokémon Fire Red Leaf Green", "audioUrl": "/audio/pokemon/pokemon-leafgreen-firered/06 Pallet Town Theme.mp3", "year": 2004, "genre": "Game Soundtrack", "console": "Game Boy Advance"}, {"id": 413, "title": "Pewter City", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon Fire Red Leaf Green", "context": "Audio - Pokémon Fire Red Leaf Green", "audioUrl": "/audio/pokemon/pokemon-leafgreen-firered/15 Pewter City Theme.mp3", "year": 2004, "genre": "Game Soundtrack", "console": "Game Boy Advance"}, {"id": 414, "title": "Battle (Vs Wild <PERSON>mon)", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon Fire Red Leaf Green", "context": "Audio - Pokémon Fire Red Leaf Green", "audioUrl": "/audio/pokemon/pokemon-leafgreen-firered/18 Battle (VS Wild Pokemon).mp3", "year": 2004, "genre": "Game Soundtrack", "console": "Game Boy Advance"}, {"id": 415, "title": "Lavender Town", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon Fire Red Leaf Green", "context": "Audio - Pokémon", "audioUrl": "/audio/pokemon/pokemon-leafgreen-firered/38 Lavender Town Theme.mp3", "year": 2004, "genre": "Game Soundtrack", "console": "Game Boy Advance"}, {"id": 416, "title": "Pokémon X Y Title Screen", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon X Y", "context": "Audio - Pokémon X Y", "audioUrl": "/audio/pokemon/pokemon-x-y/1-01. Title Screen pokemon X.mp3", "year": 2013, "genre": "Game Soundtrack", "console": "Nintendo 3DS"}, {"id": 417, "title": "Kalos Region Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon X Y", "context": "Audio - Pokémon X Y", "audioUrl": "/audio/pokemon/pokemon-x-y/1-03. Kalos Region Theme.mp3", "year": 2013, "genre": "Game Soundtrack", "console": "Nintendo 3DS"}, {"id": 418, "title": "Vaniville Town", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon X Y", "context": "Audio - Pokémon X Y", "audioUrl": "/audio/pokemon/pokemon-x-y/1-05. Vaniville Town.mp3", "year": 2013, "genre": "Game Soundtrack", "console": "Nintendo 3DS"}, {"id": 419, "title": "Aquacorde Town", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Pokémon X Y", "context": "Audio - Pokémon X Y", "audioUrl": "/audio/pokemon/pokemon-x-y/1-07. Aquacorde Town.mp3", "year": 2013, "genre": "Game Soundtrack", "console": "Nintendo 3DS"}, {"id": 420, "title": "Pokémon Ruby/Sapphire Opening Selection", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Pokémon Ruby/Sapphire", "context": "Audio - Pokémon Ruby/Sapphire", "audioUrl": "/audio/pokemon/ruby-sapphire/104 Opening Selection.mp3", "year": 2002, "genre": "JRPG Soundtrack", "console": "Game Boy Advance"}, {"id": 421, "title": "Littleroot Town", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Pokémon Ruby/Sapphire", "context": "Audio - Pokémon Ruby/Sapphire", "audioUrl": "/audio/pokemon/ruby-sapphire/105 Littleroot Town.mp3", "year": 2002, "genre": "JRPG Soundtrack", "console": "Game Boy Advance"}, {"id": 422, "title": "Route 101", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Pokémon Ruby/Sapphire", "context": "Audio - Pokémon Ruby/Sapphire", "audioUrl": "/audio/pokemon/ruby-sapphire/111 Route 101.mp3", "year": 2002, "genre": "JRPG Soundtrack", "console": "Game Boy Advance"}, {"id": 423, "title": "Battle! Trainer", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Pokémon Ruby/Sapphire", "context": "Audio - Pokémon Ruby/Sapphire", "audioUrl": "/audio/pokemon/ruby-sapphire/117 Battle! Trainer.mp3", "year": 2002, "genre": "JRPG Soundtrack", "console": "Game Boy Advance"}, {"id": 424, "title": "Rustboro City", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Pokémon Ruby/Sapphire", "context": "Audio - Pokémon Ruby/Sapphire", "audioUrl": "/audio/pokemon/ruby-sapphire/127 Rustboro City.mp3", "year": 2002, "genre": "JRPG Soundtrack", "console": "Game Boy Advance"}, {"id": 425, "title": "Dewford Town", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Pokémon Ruby/Sapphire", "context": "Audio - Pokémon Ruby/Sapphire", "audioUrl": "/audio/pokemon/ruby-sapphire/130 Dewford Town.mp3", "year": 2002, "genre": "JRPG Soundtrack", "console": "Game Boy Advance"}, {"id": 426, "title": "Verdanturf Town", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Pokémon Ruby/Sapphire", "context": "Audio - Pokémon Ruby/Sapphire", "audioUrl": "/audio/pokemon/ruby-sapphire/141 Verdanturf Town.mp3", "year": 2002, "genre": "JRPG Soundtrack", "console": "Game Boy Advance"}, {"id": 427, "title": "Title Theme", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON>", "context": "Audio - Ray<PERSON>", "audioUrl": "/audio/rayman/02 Title Theme.mp3", "year": 1995, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 428, "title": "Pink Plant Woods Area 1", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON>", "context": "Audio - Ray<PERSON>", "audioUrl": "/audio/rayman/03 Pink Plant Woods Area 1.mp3", "year": 1995, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 429, "title": "Mysterious Swamps", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Rayman Legends", "context": "Audio - Rayman Legends", "audioUrl": "/audio/rayman-legends/01. Mysterious Swamps.mp3", "year": 2013, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 430, "title": "Medieval Theme", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Rayman Legends", "context": "Audio - Rayman Legends", "audioUrl": "/audio/rayman-legends/02. Medieval Theme.mp3", "year": 2013, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 431, "title": "Storming the Castle", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Rayman Legends", "context": "Audio - Rayman Legends", "audioUrl": "/audio/rayman-legends/04. Storming the Castle.mp3", "year": 2013, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 432, "title": "Rayman Origins Intro", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Rayman Origins", "context": "Audio - Rayman Origins", "audioUrl": "/audio/rayman-origins/03. Intro.mp3", "year": 2011, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 433, "title": "Frozen Paradise", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Rayman Origins", "context": "Audio - Rayman Origins", "audioUrl": "/audio/rayman-origins/48. Frozen Paradise.mp3", "year": 2011, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 434, "title": "Glacier Cocktail", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Rayman Origins", "context": "Audio - Rayman Origins", "audioUrl": "/audio/rayman-origins/55. Glacier Cocktail.mp3", "year": 2011, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 435, "title": "Born Unto Trouble", "artist": "Bill <PERSON> & Woody <PERSON>", "composer": "Bill <PERSON> & Woody <PERSON>", "game": "Red Dead Redemption", "context": "Audio - Red Dead Redemption", "audioUrl": "/audio/red-dead-redemption/01 Born Unto Trouble.mp3", "year": 2010, "genre": "Western Soundtrack", "console": "Multi-platform"}, {"id": 436, "title": "Unshaken", "artist": "Bill <PERSON> & Woody <PERSON>", "composer": "Bill <PERSON> & Woody <PERSON>", "game": "Red Dead Redemption", "context": "Audio - Red Dead Redemption", "audioUrl": "/audio/red-dead-redemption/01. Unshaken.mp3", "year": 2010, "genre": "Western Soundtrack", "console": "Multi-platform"}, {"id": 437, "title": "The Shootist", "artist": "Bill <PERSON> & Woody <PERSON>", "composer": "Bill <PERSON> & Woody <PERSON>", "game": "Red Dead Redemption", "context": "Audio - Red Dead Redemption", "audioUrl": "/audio/red-dead-redemption/02 The Shootist.mp3", "year": 2010, "genre": "Western Soundtrack", "console": "Multi-platform"}, {"id": 438, "title": "Moonlight", "artist": "Bill <PERSON> & Woody <PERSON>", "composer": "Bill <PERSON> & Woody <PERSON>", "game": "Red Dead Redemption", "context": "Audio - Red Dead Redemption", "audioUrl": "/audio/red-dead-redemption/02. Moonlight.mp3", "year": 2010, "genre": "Western Soundtrack", "console": "Multi-platform"}, {"id": 439, "title": "Dead End Alley", "artist": "Bill <PERSON> & Woody <PERSON>", "composer": "Bill <PERSON> & Woody <PERSON>", "game": "Red Dead Redemption", "context": "Audio - Red Dead Redemption", "audioUrl": "/audio/red-dead-redemption/03 Dead End Alley.mp3", "year": 2010, "genre": "Western Soundtrack", "console": "Multi-platform"}, {"id": 440, "title": "Horseplay", "artist": "Bill <PERSON> & Woody <PERSON>", "composer": "Bill <PERSON> & Woody <PERSON>", "game": "Red Dead Redemption", "context": "Audio - Red Dead Redemption", "audioUrl": "/audio/red-dead-redemption/04 Horseplay.mp3", "year": 2010, "genre": "Western Soundtrack", "console": "Multi-platform"}, {"id": 441, "title": "R.E. 1 Save Room Safe Haven", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Resident Evil", "context": "Audio - Resident Evil", "audioUrl": "/audio/resident-evil/01 R.E. 1 Save Room- Safe Haven.mp3", "year": 1996, "genre": "Horror Soundtrack", "console": "PlayStation"}, {"id": 442, "title": "Risk of Rain", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Risk of Rain", "context": "Audio - Risk of Rain", "audioUrl": "/audio/risk-of-rain/01. Risk of Rain.mp3", "year": 2013, "genre": "Electronic", "console": "Multi-platform"}, {"id": 443, "title": "Dew Point", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Risk of Rain", "context": "Audio - Risk of Rain", "audioUrl": "/audio/risk-of-rain/02. Dew Point.mp3", "year": 2013, "genre": "Electronic", "console": "Multi-platform"}, {"id": 444, "title": "Tropic of Capricorn", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Risk of Rain", "context": "Audio - Risk of Rain", "audioUrl": "/audio/risk-of-rain/03. Tropic of Capricorn.mp3", "year": 2013, "genre": "Electronic", "console": "Multi-platform"}, {"id": 445, "title": "Ancient Rome", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Scooby-Doo and the Cyber Chase", "context": "Audio - Scooby-Doo and the Cyber Chase", "audioUrl": "/audio/scooby-doo-and-the-cyber-chase/Ancient Rome.mp3", "year": 2001, "genre": "Game Soundtrack", "console": "PlayStation"}, {"id": 446, "title": "Control Room", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Scooby-Doo and the Cyber Chase", "context": "Audio - Scooby-Doo and the Cyber Chase", "audioUrl": "/audio/scooby-doo-and-the-cyber-chase/Control Room.mp3", "year": 2001, "genre": "Game Soundtrack", "console": "PlayStation"}, {"id": 447, "title": "Japan", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Scooby-Doo and the Cyber Chase", "context": "Audio - Scooby-Doo and the Cyber Chase", "audioUrl": "/audio/scooby-doo-and-the-cyber-chase/Japan.mp3", "year": 2001, "genre": "Game Soundtrack", "console": "PlayStation"}, {"id": 448, "title": "Title Screen", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sea of Stars", "context": "Audio - Sea of Stars", "audioUrl": "/audio/sea-of-stars/1-01. Title Screen.mp3", "year": 2023, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 449, "title": "The Mountain Trail", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sea of Stars", "context": "Audio - Sea of Stars", "audioUrl": "/audio/sea-of-stars/1-03. The Mountain Trail (Day).mp3", "year": 2023, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 450, "title": "Devoted Warriors", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sea of Stars", "context": "Audio - Sea of Stars", "audioUrl": "/audio/sea-of-stars/1-05. Devoted Warriors.mp3", "year": 2023, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 451, "title": "Dance of 1,000 Suns", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sea of Stars", "context": "Audio - Sea of Stars", "audioUrl": "/audio/sea-of-stars/1-10. Dance Of 1,000 Suns.mp3", "year": 2023, "genre": "JRPG Soundtrack", "console": "Multi-platform"}, {"id": 452, "title": "Theme of Laura", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Silent Hill 2", "context": "Audio - Silent Hill 2", "audioUrl": "/audio/silent-hill2/01. Theme of Laura.mp3", "year": 2001, "genre": "Horror Soundtrack", "console": "PlayStation 2"}, {"id": 453, "title": "<PERSON>iz", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Silent Hill 2", "context": "Audio - Silent Hill 2", "audioUrl": "/audio/silent-hill2/02. White Noiz.mp3", "year": 2001, "genre": "Horror Soundtrack", "console": "PlayStation 2"}, {"id": 454, "title": "Forest", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Silent Hill 2", "context": "Audio - Silent Hill 2", "audioUrl": "/audio/silent-hill2/03. Forest.mp3", "year": 2001, "genre": "Horror Soundtrack", "console": "PlayStation 2"}, {"id": 455, "title": "Promise", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Silent Hill 2", "context": "Audio - Silent Hill 2", "audioUrl": "/audio/silent-hill2/30. Promise.mp3", "year": 2001, "genre": "Horror Soundtrack", "console": "PlayStation 2"}, {"id": 456, "title": "Before the Storm", "artist": "<PERSON>", "composer": "<PERSON>", "game": "The Elder Scrolls V: <PERSON><PERSON>", "context": "Audio - The Elder Scrolls V: <PERSON><PERSON>", "audioUrl": "/audio/skyrim/<PERSON> - The Elder Scrolls V Skyrim - The Original Game Soundtrack - 01 - Before the Storm.mp3", "year": 2011, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 457, "title": "Dragonborn", "artist": "<PERSON>", "composer": "<PERSON>", "game": "The Elder Scrolls V: <PERSON><PERSON>", "context": "Audio - The Elder Scrolls V: <PERSON><PERSON>", "audioUrl": "/audio/skyrim/<PERSON> - The Elder Scrolls V Skyrim - The Original Game Soundtrack - 01 - Dragonborn.mp3", "year": 2011, "genre": "Orchestral", "console": "Multi-platform"}, {"id": 458, "title": "Smash Bros Intro", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Super Smash Bros.", "context": "Audio - Super Smash Bros.", "audioUrl": "/audio/smash-bros/smash-bros-64/01 Intro.mp3", "year": 1999, "genre": "Game Soundtrack", "console": "Nintendo 64"}, {"id": 459, "title": "Smash Main Menu", "internalTitle": true, "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Super Smash Bros.", "context": "Audio - Super Smash Bros.", "audioUrl": "/audio/smash-bros/smash-bros-64/04 Main Menu.mp3", "year": 1999, "genre": "Game Soundtrack", "console": "Nintendo 64"}, {"id": 460, "title": "Character Select", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Super Smash Bros.", "context": "Audio - Super Smash Bros.", "audioUrl": "/audio/smash-bros/smash-bros-64/05 Character Select.mp3", "year": 1999, "genre": "Game Soundtrack", "console": "Nintendo 64"}, {"id": 461, "title": "Super Smash Bros. Brawl", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "Super Smash Bros.", "context": "Audio - Super Smash Bros.", "audioUrl": "/audio/smash-bros/smash-bros-brawl/01 - Main Theme - Super Smash Bros. Brawl.mp3", "year": 2008, "genre": "Game Soundtrack", "console": "Nintendo Wii"}, {"id": 462, "title": "Reach for the Stars", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Colors", "context": "Audio - Sonic Colors", "audioUrl": "/audio/sonic/Sonic outras/01-01_reach_for_the_stars-opening_theme/01-reach-for-the-stars-opening-theme-.mp3", "year": 2010, "genre": "Platform Game Soundtrack", "console": "Nintendo Wii"}, {"id": 463, "title": "Tropical Resort Act 1", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Colors", "context": "Audio - Sonic Colors", "audioUrl": "/audio/sonic/Sonic outras/01-03_tropical_resort-act_1/03-tropical-resort-act-1.mp3", "year": 2010, "genre": "Platform Game Soundtrack", "console": "Nintendo Wii"}, {"id": 464, "title": "Crisis City Act 2", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> the Hedgehog (2006)", "context": "Audio - <PERSON> the Hedgehog (2006)", "audioUrl": "/audio/sonic/Sonic outras/01-19_crisis_city-act_2/19-crisis-city-act-2.mp3", "year": 2006, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 465, "title": "Endless Possibilities", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Unleashed", "context": "Audio - Sonic Unleashed", "audioUrl": "/audio/sonic/Sonic outras/01-endless_possibilities/01-endless-possibilities.mp3", "year": 2008, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 466, "title": "Race to win", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Rivals 2", "context": "Audio - Sonic Rivals 2", "audioUrl": "/audio/sonic/Sonic outras/01-title_theme/01.-title-theme.mp3", "year": 2007, "genre": "Platform Game Soundtrack", "console": "PSP"}, {"id": 467, "title": "Rightthererideon", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Rush", "context": "Audio - Sonic Rush", "audioUrl": "/audio/sonic/Sonic outras/01rightthererideon/01rightthererideon.mp3", "year": 2005, "genre": "Platform Game Soundtrack", "console": "Nintendo DS"}, {"id": 468, "title": "Fist Bump", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Forces", "context": "Audio - Sonic Forces", "audioUrl": "/audio/sonic/Sonic outras/01_fist_bump_main_theme_of_sonic_forces/01-fist-bump-main-theme-of-sonic-forces-.mp3", "year": 2017, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 469, "title": "Un Gravitify", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sonic Riders: <PERSON> Gra<PERSON>", "context": "Audio - Sonic Riders: Zero Gravity", "audioUrl": "/audio/sonic/Sonic outras/01_un-gravitify/01_un-gravitify.mp3", "year": 2008, "genre": "Racing Game Soundtrack", "console": "Multi-platform"}, {"id": 470, "title": "Starlight Carnival Act 1", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Colors", "context": "Audio - Sonic Colors", "audioUrl": "/audio/sonic/Sonic outras/02-03_starlight_carnival-act_1/03-starlight-carnival-act-1.mp3", "year": 2010, "genre": "Platform Game Soundtrack", "console": "Nintendo Wii"}, {"id": 471, "title": "Planet Wisp Act 1", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Colors", "context": "Audio - Sonic Colors", "audioUrl": "/audio/sonic/Sonic outras/02-12_planet_wisp-act_1/12-planet-wisp-act-1.mp3", "year": 2010, "genre": "Platform Game Soundtrack", "console": "Nintendo Wii"}, {"id": 472, "title": "Back2back", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Rush", "context": "Audio - Sonic Rush", "audioUrl": "/audio/sonic/Sonic outras/02back2back/02back2back.mp3", "year": 2005, "genre": "Platform Game Soundtrack", "console": "Nintendo DS"}, {"id": 473, "title": "Apotos Day", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Unleashed", "context": "Audio - Sonic Unleashed", "audioUrl": "/audio/sonic/Sonic outras/07-apotos_day/07-apotos-day.mp3", "year": 2008, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 474, "title": "Apotos Windmill Isle Day", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Unleashed", "context": "Audio - Sonic Unleashed", "audioUrl": "/audio/sonic/Sonic outras/08-apotos_windmill_isle_day/08-apotos-windmill-isle-day.mp3", "year": 2008, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 475, "title": "Opening Movie", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Mania", "context": "Audio - Sonic Mania", "audioUrl": "/audio/sonic/Sonic outras/1-01_opening_movie/1-01-opening-movie.mp3", "year": 2017, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 476, "title": "Lights Camera Action Studiopolis Zone Act 1", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Mania", "context": "Audio - Sonic Mania", "audioUrl": "/audio/sonic/Sonic outras/1-14_lights_camera_action_studiopolis_zone_act_1/1-14-lights-camera-action-studiopolis-zone-act-1-.mp3", "year": 2017, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 477, "title": "Tabloid Jargon Press Garden Zone Act 1", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Mania", "context": "Audio - Sonic Mania", "audioUrl": "/audio/sonic/Sonic outras/1-19_tabloid_jargon_press_garden_zone_act_1/1-19-tabloid-jargon-press-garden-zone-act-1-.mp3", "year": 2017, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 478, "title": "Seven Rings in Hand", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic and the Secret Rings", "context": "Audio - Sonic and the Secret Rings", "audioUrl": "/audio/sonic/Sonic outras/101_seven_rings_in_hand/101-seven-rings-in-hand.mp3", "year": 2007, "genre": "Platform Game Soundtrack", "console": "Nintendo Wii"}, {"id": 479, "title": "Ma<PERSON>ri Savannah Citadel Day", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Unleashed", "context": "Audio - Sonic Unleashed", "audioUrl": "/audio/sonic/Sonic outras/16-mazuri_savannah_citadel_day/16-mazuri-savannah-citadel-day.mp3", "year": 2008, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 480, "title": "Spagonia Day", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Unleashed", "context": "Audio - Sonic Unleashed", "audioUrl": "/audio/sonic/Sonic outras/21-spagonia_day/21-spagonia-day.mp3", "year": 2008, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 481, "title": "Spagonia Rooftop Run Day", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Unleashed", "context": "Audio - Sonic Unleashed", "audioUrl": "/audio/sonic/Sonic outras/22-spagonia_rooftop_run_day/22-spagonia-rooftop-run-day.mp3", "year": 2008, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 482, "title": "<PERSON>", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Unleashed", "context": "Audio - Sonic Unleashed", "audioUrl": "/audio/sonic/Sonic outras/31-chun-nan_day/31-chun-nan-day.mp3", "year": 2008, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 483, "title": "Eggman Land", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Unleashed", "context": "Audio - Sonic Unleashed", "audioUrl": "/audio/sonic/Sonic outras/49-eggman_land_theme_of_eggman/49-eggman-land-theme-of-eggman.mp3", "year": 2008, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 484, "title": "Dreams of an Absolution", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> the Hedgehog (2006)", "context": "Audio - <PERSON> the Hedgehog (2006)", "audioUrl": "/audio/sonic/Sonic outras/dreams_of_an_absolution-lee_brotherton/dreams-of-an-absolution-lee-brotherton-sonic-the-hedgehog-2006-.mp3", "year": 2006, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 485, "title": "His World", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> the Hedgehog (2006)", "context": "Audio - <PERSON> the Hedgehog (2006)", "audioUrl": "/audio/sonic/<PERSON> outras/his_world-ali_tabatabaee_matty_lewis/his-world-ali-tabatabaee-matty-lewis-sonic-the-hedgehog-2006-.mp3", "year": 2006, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 487, "title": "All Hail Shadow", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON> the Hedgehog", "context": "Audio - <PERSON> the Hedgehog", "audioUrl": "/audio/sonic/Sonic outras/lost-all_hail_shadow/all-hail-shadow-magna-fi-shadow-the-hedgehog.mp3", "year": 2005, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 488, "title": "I Am... All of Me", "artist": "<PERSON>", "composer": "<PERSON>", "game": "<PERSON> the Hedgehog", "context": "Audio - <PERSON> the Hedgehog", "audioUrl": "/audio/sonic/Sonic outras/lost-i_am/i-am...-all-of-me-crush-40-shadow-the-hedgehog.mp3", "year": 2005, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 489, "title": "It Doesnt Matter", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure", "context": "Audio - Sonic Adventure", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure01_songs_with_attitude/01-it-doesnt-matter-theme-of-sonic-.mp3", "year": 1998, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 490, "title": "My Sweet Passion", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure", "context": "Audio - Sonic Adventure", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure01_songs_with_attitude/02-my-sweet-passion-theme-of-amy-.mp3", "year": 1998, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 491, "title": "Lazy Days Livin in Paradise", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure", "context": "Audio - Sonic Adventure", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure01_songs_with_attitude/03-lazy-days-livin-in-paradaise-theme-of-big-.mp3", "year": 1998, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 492, "title": "Believe in Myself", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure", "context": "Audio - Sonic Adventure", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure01_songs_with_attitude/04-believe-in-myself-theme-of-miles-.mp3", "year": 1998, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 493, "title": "Unknown from M.E.", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure", "context": "Audio - Sonic Adventure", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure01_songs_with_attitude/05-unknown-from-me-theme-of-knuckles-.mp3", "year": 1998, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 494, "title": "Open Your Heart", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure", "context": "Audio - Sonic Adventure", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure01_songs_with_attitude/06-open-your-heart-main-theme-of-sonic-adventure-.mp3", "year": 1998, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 501, "title": "It Doesn't Matter", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure 2", "context": "Audio - Sonic Adventure 2", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure02_vocal/01-it-doesn-t-matter.mp3", "year": 2001, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 502, "title": "Escape from the City", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure 2", "context": "Audio - Sonic Adventure 2", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure02_vocal/02-escape-from-the-city.mp3", "year": 2001, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 503, "title": "Believe in Myself", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure 2", "context": "Audio - Sonic Adventure 2", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure02_vocal/03-believe-in-myself.mp3", "year": 2001, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 505, "title": "Fly in the Freedom", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure 2", "context": "Audio - Sonic Adventure 2", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure02_vocal/05-fly-in-the-freedom.mp3", "year": 2001, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 506, "title": "Throw It All Away", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure 2", "context": "Audio - Sonic Adventure 2", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure02_vocal/06-throw-it-all-away.mp3", "year": 2001, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 507, "title": "<PERSON><PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure 2", "context": "Audio - Sonic Adventure 2", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure02_vocal/07_eggman.mp3", "year": 2001, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 508, "title": "Live and Learn", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Sonic Adventure 2", "context": "Audio - Sonic Adventure 2", "audioUrl": "/audio/sonic/Sonic outras/sonic_adventure02_vocal/08-live-and-learn.mp3", "year": 2001, "genre": "Platform Game Soundtrack", "console": "Dreamcast"}, {"id": 517, "title": "Knight of the Wind", "artist": "Crush 40", "composer": "Crush 40", "game": "<PERSON> and the Black Knight", "context": "Audio - Sonic and the Black Knight", "audioUrl": "/audio/sonic/Sonic outras/vocal-01_knight_of_the_wind/01-knight-of-the-wind.mp3", "year": 2009, "genre": "Platform Game Soundtrack", "console": "Nintendo Wii"}, {"id": 519, "title": "Green Hill Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> the Hedgehog", "context": "Audio - <PERSON> the Hedgehog", "audioUrl": "/audio/sonic/sonic-1/02 - Green Hill Zone - Sonic the Hedgehog - Ma<PERSON><PERSON> Nakamura.mp3", "year": 1991, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 520, "title": "Marble Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> the Hedgehog", "context": "Audio - <PERSON> the Hedgehog", "audioUrl": "/audio/sonic/sonic-1/03 - Marble Zone - Sonic the Hedgehog - Ma<PERSON><PERSON> Nakamura.mp3", "year": 1991, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 521, "title": "Spring Yard Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> the Hedgehog", "context": "Audio - <PERSON> the Hedgehog", "audioUrl": "/audio/sonic/sonic-1/04 - Spring Yard Zone - Sonic the Hedgehog - Ma<PERSON><PERSON> Nakamura.mp3", "year": 1991, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 522, "title": "Labyrinth Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> the Hedgehog", "context": "Audio - <PERSON> the Hedgehog", "audioUrl": "/audio/sonic/sonic-1/05 - Labyrinth Zone - Sonic the Hedgehog - Ma<PERSON><PERSON> Nakamura.mp3", "year": 1991, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 523, "title": "Star Light Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> the Hedgehog", "context": "Audio - <PERSON> the Hedgehog", "audioUrl": "/audio/sonic/sonic-1/06 - Star Light Zone - Sonic the Hedgehog - Ma<PERSON><PERSON> Nakamura.mp3", "year": 1991, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 524, "title": "Scrap Brain Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> the Hedgehog", "context": "Audio - <PERSON> the Hedgehog", "audioUrl": "/audio/sonic/sonic-1/07 - Scrap Brain Zone - Sonic the Hedgehog - Ma<PERSON><PERSON> Nakamura.mp3", "year": 1991, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 525, "title": "Robotnik", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "<PERSON> the Hedgehog", "context": "Audio - <PERSON> the Hedgehog", "audioUrl": "/audio/sonic/sonic-1/16 - <PERSON><PERSON> - Sonic the Hedgehog - <PERSON><PERSON><PERSON>.mp3", "year": 1991, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 526, "title": "Title Theme", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Donkey Kong Country", "context": "Audio - Donkey Kong Country", "audioUrl": "/audio/donkey-kong/donkey-kong-country/01 - Title Screen - Donkey Kong Country - <PERSON>.mp3", "year": 1994, "genre": "Platform Game Soundtrack", "console": "SNES"}, {"id": 527, "title": "Emerald Hill Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sonic the Hedgehog 2", "context": "Audio - Sonic the Hedgehog 2", "audioUrl": "/audio/sonic/sonic-2/03 - Emerald Hill Zone - Sonic the Hedgehog 2 - <PERSON><PERSON><PERSON>.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 528, "title": "Chemical Plant Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sonic the Hedgehog 2", "context": "Audio - Sonic the Hedgehog 2", "audioUrl": "/audio/sonic/sonic-2/05 - Chemical Plant Zone - Sonic the Hedgehog 2 - <PERSON><PERSON><PERSON>.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 529, "title": "Aquatic Ruin Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sonic the Hedgehog 2", "context": "Audio - Sonic the Hedgehog 2", "audioUrl": "/audio/sonic/sonic-2/07 - Aquatic Ruin Zone - Sonic the Hedgehog 2 - <PERSON><PERSON><PERSON>.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 530, "title": "Casino Night Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sonic the Hedgehog 2", "context": "Audio - Sonic the Hedgehog 2", "audioUrl": "/audio/sonic/sonic-2/08 - Casino Night Zone - Sonic the Hedgehog 2 - <PERSON><PERSON><PERSON>.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 531, "title": "Hill Top Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sonic the Hedgehog 2", "context": "Audio - Sonic the Hedgehog 2", "audioUrl": "/audio/sonic/sonic-2/09 - Hill Top Zone - Sonic the Hedgehog 2 - <PERSON><PERSON><PERSON>.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 532, "title": "Mystic Cave Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sonic the Hedgehog 2", "context": "Audio - Sonic the Hedgehog 2", "audioUrl": "/audio/sonic/sonic-2/10 - Mystic Cave Zone - Sonic the Hedgehog 2 - <PERSON><PERSON><PERSON>.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 533, "title": "Oil Ocean Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sonic the Hedgehog 2", "context": "Audio - Sonic the Hedgehog 2", "audioUrl": "/audio/sonic/sonic-2/11 - Oil Ocean Zone - Sonic the Hedgehog 2 - <PERSON><PERSON><PERSON>.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 534, "title": "Metropolis Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sonic the Hedgehog 2", "context": "Audio - Sonic the Hedgehog 2", "audioUrl": "/audio/sonic/sonic-2/12 - Metropolis Zone - Sonic the Hedgehog 2 - <PERSON><PERSON><PERSON>.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 535, "title": "Death Egg Zone", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Sonic the Hedgehog 2", "context": "Audio - Sonic the Hedgehog 2", "audioUrl": "/audio/sonic/sonic-2/15 - Death Egg Zone - Sonic the Hedgehog 2 - <PERSON><PERSON><PERSON>.mp3", "year": 1992, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 536, "title": "Angel Island Zone Act 1", "artist": "<PERSON> & Sega Sound Team", "composer": "<PERSON> & Sega Sound Team", "game": "Sonic the Hedgehog 3", "context": "Audio - Sonic the Hedgehog 3", "audioUrl": "/audio/sonic/sonic-3/03 - Angel Island Zone Act 1 - Sonic the Hedgehog 3 - <PERSON> & Sega Sound Team.mp3", "year": 1994, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 537, "title": "Icecap Zone Act 1", "artist": "<PERSON> & Sega Sound Team", "composer": "<PERSON> & Sega Sound Team", "game": "Sonic the Hedgehog 3", "context": "Audio - Sonic the Hedgehog 3", "audioUrl": "/audio/sonic/sonic-3/11 - IceCap Zone Act 1 - <PERSON> the Hedgehog 3 - <PERSON> & Sega Sound Team.mp3", "year": 1994, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 538, "title": "Launch Base Zone Act 1", "artist": "<PERSON> & Sega Sound Team", "composer": "<PERSON> & Sega Sound Team", "game": "Sonic the Hedgehog 3", "context": "Audio - Sonic the Hedgehog 3", "audioUrl": "/audio/sonic/sonic-3/13 - Launch Base Zone Act 1 - Sonic the Hedgehog 3 - <PERSON> & Sega Sound Team.mp3", "year": 1994, "genre": "Platform Game Soundtrack", "console": "Sega Genesis"}, {"id": 539, "title": "Find Your Flane", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Frontiers", "context": "Audio - Sonic Frontiers", "audioUrl": "/audio/sonic/sonic-frontiers/youtube_4gzIL8_G4Xs_audio.mp3", "year": 2022, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 540, "title": "Break Through It All", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Frontiers", "context": "Audio - Sonic Frontiers", "audioUrl": "/audio/sonic/sonic-frontiers/youtube_AzSIuBwPAKs_audio.mp3", "year": 2022, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 541, "title": "I'm Here", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Frontiers", "context": "Audio - Sonic Frontiers", "audioUrl": "/audio/sonic/sonic-frontiers/youtube_mUJS9ystZlc_audio.mp3", "year": 2022, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 542, "title": "Vandalize", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Frontiers", "context": "Audio - Sonic Frontiers", "audioUrl": "/audio/sonic/sonic-frontiers/youtube_neexlOTmAxw_audio.mp3", "year": 2022, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 543, "title": "Dear Father", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Frontiers", "context": "Audio - Sonic Frontiers", "audioUrl": "/audio/sonic/sonic-frontiers/youtube_oyVKV5BWzhk_audio.mp3", "year": 2022, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 544, "title": "One Way Dream", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Frontiers", "context": "Audio - Sonic Frontiers", "audioUrl": "/audio/sonic/sonic-frontiers/youtube_TYtGaUQLJC0_audio.mp3", "year": 2022, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 545, "title": "Undefeatable", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Sonic Frontiers", "context": "Audio - Sonic Frontiers", "audioUrl": "/audio/sonic/sonic-frontiers/youtube_u_FRDqHT5y0_audio.mp3", "year": 2022, "genre": "Platform Game Soundtrack", "console": "Multi-platform"}, {"id": 546, "title": "Breathe", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Spirit of the North", "context": "Audio - Spirit of the North", "audioUrl": "/audio/spirit-of-the-north/Breathe.mp3", "year": 2019, "genre": "Ambient", "console": "Multi-platform"}, {"id": 547, "title": "Climbing", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Spirit of the North", "context": "Audio - Spirit of the North", "audioUrl": "/audio/spirit-of-the-north/Climbing.mp3", "year": 2019, "genre": "Ambient", "console": "Multi-platform"}, {"id": 548, "title": "Cold and Distant", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Spirit of the North", "context": "Audio - Spirit of the North", "audioUrl": "/audio/spirit-of-the-north/Cold and Distant.mp3", "year": 2019, "genre": "Ambient", "console": "Multi-platform"}, {"id": 549, "title": "Departed", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Spirit of the North", "context": "Audio - Spirit of the North", "audioUrl": "/audio/spirit-of-the-north/Departed.mp3", "year": 2019, "genre": "Ambient", "console": "Multi-platform"}, {"id": 550, "title": "Exodus", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Spirit of the North", "context": "Audio - Spirit of the North", "audioUrl": "/audio/spirit-of-the-north/Exodus.mp3", "year": 2019, "genre": "Ambient", "console": "Multi-platform"}, {"id": 551, "title": "Splattack!", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Splatoon", "context": "Audio - Splatoon", "audioUrl": "/audio/splatoon/1-03. Splattack!.mp3", "year": 2015, "genre": "Shooter Game Soundtrack", "console": "Wii U"}, {"id": 552, "title": "Ink Or Sink", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Splatoon", "context": "Audio - Splatoon", "audioUrl": "/audio/splatoon/1-04. Ink or Sink.mp3", "year": 2015, "genre": "Shooter Game Soundtrack", "console": "Wii U"}, {"id": 553, "title": "Corneria", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Star Fox", "context": "Audio - Star Fox", "audioUrl": "/audio/star-fox/109 Corneria.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 554, "title": "Fortuna", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Star Fox", "context": "Audio - Star Fox", "audioUrl": "/audio/star-fox/112 Fortuna.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 555, "title": "<PERSON><PERSON>", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Star Fox", "context": "Audio - Star Fox", "audioUrl": "/audio/star-fox/113 <PERSON>beth.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 556, "title": "Star Fox Main Theme", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Star Fox", "context": "Audio - Star Fox", "audioUrl": "/audio/star-fox/136 Main Theme.mp3", "year": 1993, "genre": "Game Soundtrack", "console": "SNES"}, {"id": 557, "title": "Overture", "artist": "Concerned<PERSON><PERSON>", "composer": "Concerned<PERSON><PERSON>", "game": "Stardew Valley", "context": "Audio - Stardew Valley", "audioUrl": "/audio/stardew valley/01. Stardew Valley Overture.mp3", "year": 2016, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 558, "title": "Cloud Country", "artist": "Concerned<PERSON><PERSON>", "composer": "Concerned<PERSON><PERSON>", "game": "Stardew Valley", "context": "Audio - Stardew Valley", "audioUrl": "/audio/stardew valley/02. Cloud Country.mp3", "year": 2016, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 559, "title": "Spring (It's a Big World Outside)", "artist": "Concerned<PERSON><PERSON>", "composer": "Concerned<PERSON><PERSON>", "game": "Stardew Valley", "context": "Audio - Stardew Valley", "audioUrl": "/audio/stardew valley/05. Spring (It's A Big World Outside).mp3", "year": 2016, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 560, "title": "Pelican Town", "artist": "Concerned<PERSON><PERSON>", "composer": "Concerned<PERSON><PERSON>", "game": "Stardew Valley", "context": "Audio - Stardew Valley", "audioUrl": "/audio/stardew valley/08. Pelican Town.mp3", "year": 2016, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 561, "title": "Summer (Nature's Crescendo)", "artist": "Concerned<PERSON><PERSON>", "composer": "Concerned<PERSON><PERSON>", "game": "Stardew Valley", "context": "Audio - Stardew Valley", "audioUrl": "/audio/stardew valley/13. Summer (Nature's Crescendo).mp3", "year": 2016, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 562, "title": "Fall (the Smell of Mushroom)", "artist": "Concerned<PERSON><PERSON>", "composer": "Concerned<PERSON><PERSON>", "game": "Stardew Valley", "context": "Audio - Stardew Valley", "audioUrl": "/audio/stardew valley/20. Fall (The Smell Of Mushroom).mp3", "year": 2016, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 563, "title": "Winter (Nocturne of Ice)", "artist": "Concerned<PERSON><PERSON>", "composer": "Concerned<PERSON><PERSON>", "game": "Stardew Valley", "context": "Audio - Stardew Valley", "audioUrl": "/audio/stardew valley/27. Winter (Nocturne Of Ice).mp3", "year": 2016, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 564, "title": "Mines (Crystal Bells)", "artist": "Concerned<PERSON><PERSON>", "composer": "Concerned<PERSON><PERSON>", "game": "Stardew Valley", "context": "Audio - Stardew Valley", "audioUrl": "/audio/stardew valley/63. Mines (Crystal Bells).mp3", "year": 2016, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 565, "title": "The World Warrior", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Street Fighter", "context": "Audio - Street Fighter", "audioUrl": "/audio/street-fighter/street-fighter-2/02 The World Warrior.mp3", "year": 1987, "genre": "Fighting Game Soundtrack", "console": "Arcade"}, {"id": 566, "title": "Player Select", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Street Fighter", "context": "Audio - Street Fighter", "audioUrl": "/audio/street-fighter/street-fighter-2/03 Player Select.mp3", "year": 1987, "genre": "Fighting Game Soundtrack", "console": "Arcade"}, {"id": 567, "title": "<PERSON><PERSON>", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Street Fighter", "context": "Audio - Street Fighter", "audioUrl": "/audio/street-fighter/street-fighter-2/05 Ryu.mp3", "year": 1987, "genre": "Fighting Game Soundtrack", "console": "Arcade"}, {"id": 568, "title": "Guile", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Street Fighter", "context": "Audio - Street Fighter", "audioUrl": "/audio/street-fighter/street-fighter-2/08 Guile.mp3", "year": 1987, "genre": "Fighting Game Soundtrack", "console": "Arcade"}, {"id": 569, "title": "Player Select V2", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Streets of Rage", "context": "Audio - Streets of Rage", "audioUrl": "/audio/streets-of-rage/streets-of-rage-2/01 - Player Select V2.mp3", "year": 1991, "genre": "Electronic", "console": "Sega Genesis"}, {"id": 570, "title": "Go Straight", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Streets of Rage", "context": "Audio - Streets of Rage", "audioUrl": "/audio/streets-of-rage/streets-of-rage-2/02 - Go Straight.mp3", "year": 1991, "genre": "Electronic", "console": "Sega Genesis"}, {"id": 571, "title": "In the Bar", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Streets of Rage", "context": "Audio - Streets of Rage", "audioUrl": "/audio/streets-of-rage/streets-of-rage-2/03 - In the Bar.mp3", "year": 1991, "genre": "Electronic", "console": "Sega Genesis"}, {"id": 572, "title": "Dreamer", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "Streets of Rage", "context": "Audio - Streets of Rage", "audioUrl": "/audio/streets-of-rage/streets-of-rage-2/07 - Dreamer.mp3", "year": 1991, "genre": "Electronic", "console": "Sega Genesis"}, {"id": 573, "title": "Salutations", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Subnautica", "context": "Audio - Subnautica", "audioUrl": "/audio/subnautica/1 - Salutations.mp3", "year": 2018, "genre": "Ambient", "console": "Multi-platform"}, {"id": 574, "title": "Into the Unknown", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Subnautica", "context": "Audio - Subnautica", "audioUrl": "/audio/subnautica/2 - Into the Unknown.mp3", "year": 2018, "genre": "Ambient", "console": "Multi-platform"}, {"id": 575, "title": "Tropical Eden", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Subnautica", "context": "Audio - Subnautica", "audioUrl": "/audio/subnautica/3 - Tropical Eden.mp3", "year": 2018, "genre": "Ambient", "console": "Multi-platform"}, {"id": 576, "title": "Overworld Day", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Terraria", "context": "Audio - Terraria", "audioUrl": "/audio/terraria/1-01. Overworld Day.mp3", "year": 2011, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 577, "title": "Overworld Night", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Terraria", "context": "Audio - Terraria", "audioUrl": "/audio/terraria/1-03. Overworld Night.mp3", "year": 2011, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 578, "title": "Boss 1", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Terraria", "context": "Audio - Terraria", "audioUrl": "/audio/terraria/1-06. Boss 1.mp3", "year": 2011, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 579, "title": "Jungle", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Terraria", "context": "Audio - Terraria", "audioUrl": "/audio/terraria/1-07. Jungle.mp3", "year": 2011, "genre": "Game Soundtrack", "console": "Multi-platform"}, {"id": 580, "title": "Title", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - Tetris", "audioUrl": "/audio/tetris/01 Title.mp3", "year": 1984, "genre": "Puzzle Game Soundtrack", "console": "Multi-platform"}, {"id": 581, "title": "B Type Music", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - Tetris", "audioUrl": "/audio/tetris/03 B-Type Music.mp3", "year": 1984, "genre": "Puzzle Game Soundtrack", "console": "Multi-platform"}, {"id": 582, "title": "<PERSON><PERSON><PERSON>", "artist": "<PERSON><PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON><PERSON>", "context": "Audio - Tetris", "audioUrl": "/audio/tetris/tetris.mp3", "year": 1984, "genre": "Puzzle Game Soundtrack", "console": "Multi-platform"}, {"id": 583, "title": "The Quarantine Zone [20 Years Later]", "artist": "<PERSON>", "composer": "<PERSON>", "game": "The Last of Us", "context": "Audio - The Last of Us", "audioUrl": "/audio/the-last-of-us/01. The Quarantine Zone [20 Years Later].mp3", "year": 2013, "genre": "Post-Apocalyptic Soundtrack", "console": "PlayStation"}, {"id": 584, "title": "The Last of Us", "artist": "<PERSON>", "composer": "<PERSON>", "game": "The Last of Us", "context": "Audio - The Last of Us", "audioUrl": "/audio/the-last-of-us/03. The Last of Us.mp3", "year": 2013, "genre": "Post-Apocalyptic Soundtrack", "console": "PlayStation"}, {"id": 585, "title": "Breathless", "artist": "<PERSON>", "composer": "<PERSON>", "game": "The Last of Us", "context": "Audio - The Last of Us", "audioUrl": "/audio/the-last-of-us/26. Breathless.mp3", "year": 2013, "genre": "Post-Apocalyptic Soundtrack", "console": "PlayStation"}, {"id": 586, "title": "It's the Sims", "artist": "<PERSON>", "composer": "<PERSON>", "game": "The Sims 4", "context": "Audio - The Sims 4", "audioUrl": "/audio/the-sims/01 - It's the Sims.mp3", "year": 2014, "genre": "Simulation Game Soundtrack", "console": "Multi-platform"}, {"id": 587, "title": "The Sims Main Theme", "artist": "<PERSON>", "composer": "<PERSON>", "game": "The Sims", "context": "Audio - <PERSON>", "audioUrl": "/audio/the-sims/1-01. Main Theme.mp3", "year": 2000, "genre": "Simulation Game Soundtrack", "console": "Multi-platform"}, {"id": 588, "title": "Buy Mode 1", "artist": "<PERSON>", "composer": "<PERSON>", "game": "The Sims", "context": "Audio - <PERSON>", "audioUrl": "/audio/the-sims/Buy Mode 1.mp3", "year": 2000, "genre": "Simulation Game Soundtrack", "console": "Multi-platform"}, {"id": 589, "title": "Everything's Alright", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "To the Moon", "context": "Audio - To the Moon", "audioUrl": "/audio/to-the-moon/Everything's Alright.mp3", "year": 2011, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 590, "title": "Piano", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "To the Moon", "context": "Audio - To the Moon", "audioUrl": "/audio/to-the-moon/For River - Piano (Johnny's Version).mp3", "year": 2011, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 591, "title": "<PERSON><PERSON><PERSON>", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "To the Moon", "context": "Audio - To the Moon", "audioUrl": "/audio/to-the-moon/Moonwisher.mp3", "year": 2011, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 592, "title": "Once Upon a Memory", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "To the Moon", "context": "Audio - To the Moon", "audioUrl": "/audio/to-the-moon/Once Upon a Memory.mp3", "year": 2011, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 593, "title": "To the Moon Main Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "To the Moon", "context": "Audio - To the Moon", "audioUrl": "/audio/to-the-moon/To the Moon - Main Theme.mp3", "year": 2011, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 594, "title": "Piano (Ending Version)", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "To the Moon", "context": "Audio - To the Moon", "audioUrl": "/audio/to-the-moon/To the Moon - Piano (Ending Version).mp3", "year": 2011, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 595, "title": "Title", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Top Gear", "context": "Audio - Top Gear", "audioUrl": "/audio/top-gear/01 Title Top Gear.mp3", "year": 1992, "genre": "Racing Game Soundtrack", "console": "SNES"}, {"id": 596, "title": "Las Vegas", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Top Gear", "context": "Audio - Top Gear", "audioUrl": "/audio/top-gear/02 Las Vegas.mp3", "year": 1992, "genre": "Racing Game Soundtrack", "console": "SNES"}, {"id": 597, "title": "Bordeaux", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Top Gear", "context": "Audio - Top Gear", "audioUrl": "/audio/top-gear/04 Bordeaux.mp3", "year": 1992, "genre": "Racing Game Soundtrack", "console": "SNES"}, {"id": 598, "title": "Frankfurt", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Top Gear", "context": "Audio - Top Gear", "audioUrl": "/audio/top-gear/05 Frankfurt.mp3", "year": 1992, "genre": "Racing Game Soundtrack", "console": "SNES"}, {"id": 599, "title": "Altars of Apostasy", "artist": "<PERSON> Pierce Her", "composer": "<PERSON> Pierce Her", "game": "ULTRAKILL", "context": "Audio - ULTRAKILL", "audioUrl": "/audio/ultrakill/Altars of Apostasy.mp3", "year": 2020, "genre": "FPS Soundtrack", "console": "PC"}, {"id": 600, "title": "War Without Reason", "artist": "<PERSON> Pierce Her", "composer": "<PERSON> Pierce Her", "game": "ULTRAKILL", "context": "Audio - ULTRAKILL", "audioUrl": "/audio/ultrakill/<PERSON> Pierce Her - War Without Reason (ULTRAKILL 7-4 Theme).mp3", "year": 2020, "genre": "FPS Soundtrack", "console": "PC"}, {"id": 601, "title": "Tenebre Ross<PERSON>", "artist": "<PERSON> Pierce Her", "composer": "<PERSON> Pierce Her", "game": "ULTRAKILL", "context": "Audio - ULTRAKILL", "audioUrl": "/audio/ultrakill/<PERSON>EY<PERSON>N CHURCH - <PERSON><PERSON><PERSON> (ULTRAKILL Soundtrack).mp3", "year": 2020, "genre": "FPS Soundtrack", "console": "PC"}, {"id": 602, "title": "The World Looks Red", "artist": "<PERSON> Pierce Her", "composer": "<PERSON> Pierce Her", "game": "ULTRAKILL", "context": "Audio - ULTRAKILL", "audioUrl": "/audio/ultrakill/The World Looks Red.mp3", "year": 2020, "genre": "FPS Soundtrack", "console": "PC"}, {"id": 603, "title": "Goldenslaughterer", "artist": "ZTS", "composer": "ZTS", "game": "<PERSON>ineko When They Cry", "context": "Audio - <PERSON><PERSON><PERSON> When They Cry", "audioUrl": "/audio/umineko/26. goldenslaughterer.mp3", "year": 2007, "genre": "Visual Novel Soundtrack", "console": "PC"}, {"id": 604, "title": "Far (Flat)", "artist": "ZTS", "composer": "ZTS", "game": "<PERSON>ineko When They Cry", "context": "Audio - <PERSON><PERSON><PERSON> When They Cry", "audioUrl": "/audio/umineko/far (flat).mp3", "year": 2007, "genre": "Visual Novel Soundtrack", "console": "PC"}, {"id": 605, "title": "Hope", "artist": "ZTS", "composer": "ZTS", "game": "<PERSON>ineko When They Cry", "context": "Audio - <PERSON><PERSON><PERSON> When They Cry", "audioUrl": "/audio/umineko/Hope.mp3", "year": 2007, "genre": "Visual Novel Soundtrack", "console": "PC"}, {"id": 606, "title": "The Girl's Witch Hunt", "artist": "ZTS", "composer": "ZTS", "game": "<PERSON>ineko When They Cry", "context": "Audio - <PERSON><PERSON><PERSON> When They Cry", "audioUrl": "/audio/um<PERSON><PERSON>/The girl's witch hunt.mp3", "year": 2007, "genre": "Visual Novel Soundtrack", "console": "PC"}, {"id": 607, "title": "<PERSON><PERSON><PERSON>rr<PERSON>", "artist": "ZTS", "composer": "ZTS", "game": "<PERSON>ineko When They Cry", "context": "Audio - <PERSON><PERSON><PERSON> When They Cry", "audioUrl": "/audio/um<PERSON><PERSON>/<PERSON><PERSON><PERSON>rrup<PERSON>.mp3", "year": 2007, "genre": "Visual Novel Soundtrack", "console": "PC"}, {"id": 608, "title": "Once Upon a Time", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 001 Once Upon a Time.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 609, "title": "Your Best Friend", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 003 Your Best Friend.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 610, "title": "<PERSON><PERSON><PERSON>", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 005 Ruins.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 611, "title": "Enemy Approaching", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 009 Enemy Approaching.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 612, "title": "Heartache", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 014 Heartache.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 613, "title": "Sans", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 015 sans..mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 614, "title": "Snowy", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 017 Snowy.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 615, "title": "Bone<PERSON>usle", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 024 Bonetrousle.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 616, "title": "Waterfall", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 031 Waterfall.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 617, "title": "Memory", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 034 Memory.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 618, "title": "Spider Dance", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 059 Spider Dance.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 619, "title": "It's Raining Somewhere Else", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 063 It's Raining Somewhere Else.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 620, "title": "Core", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 065 CORE.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 621, "title": "Death by Glamour", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 068 Death by Glamour.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 622, "title": "Undertale", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 071 Undertale.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 623, "title": "An Ending", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 081 An Ending.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 624, "title": "Hopes and Dreams", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 087 Hopes and Dreams.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 625, "title": "Save the World", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 089 SAVE the World.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 626, "title": "Final Power", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 091 Final Power.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 627, "title": "Respite", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 094 Respite.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 628, "title": "Battle Against a True Hero", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 098 Battle Against a True Hero.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 629, "title": "Megalovania", "artist": "<PERSON>", "composer": "<PERSON>", "game": "Undertale", "context": "Audio - Undertale", "audioUrl": "/audio/undertale/Undertale OST - 100 MEGALOVANIA.mp3", "year": 2015, "genre": "Indie Game Soundtrack", "console": "Multi-platform"}, {"id": 630, "title": "Memories", "artist": "Nico <PERSON>", "composer": "Nico <PERSON>", "game": "Until Then", "context": "Audio - Until Then", "audioUrl": "/audio/until-then/01. Memories.mp3", "year": 2024, "genre": "Visual Novel Soundtrack", "console": "Multi-platform"}, {"id": 631, "title": "Time Is Tearing Us Apart", "artist": "Nico <PERSON>", "composer": "Nico <PERSON>", "game": "Until Then", "context": "Audio - Until Then", "audioUrl": "/audio/until-then/21. Time Is Tearing Us Apart.mp3", "year": 2024, "genre": "Visual Novel Soundtrack", "console": "Multi-platform"}, {"id": 632, "title": "The World Hasn't Ended Yet", "artist": "Nico <PERSON>", "composer": "Nico <PERSON>", "game": "Until Then", "context": "Audio - Until Then", "audioUrl": "/audio/until-then/75. The World Hasn't Ended Yet.mp3", "year": 2024, "genre": "Visual Novel Soundtrack", "console": "Multi-platform"}, {"id": 633, "title": "Mii Channel", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Wii System Music", "context": "Audio - Wii System Music", "audioUrl": "/audio/wii/03. Mii Channel.mp3", "year": 2006, "genre": "System Music", "console": "Wii"}, {"id": 634, "title": "Shop Channel", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Wii System Music", "context": "Audio - Wii System Music", "audioUrl": "/audio/wii/07. Shop Channel.mp3", "year": 2006, "genre": "System Music", "console": "Wii"}, {"id": 635, "title": "Title", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "Wii System Music", "context": "Audio - Wii System Music", "audioUrl": "/audio/wii/08. Title (Wii Sports).mp3", "year": 2006, "genre": "System Music", "console": "Wii"}, {"id": 636, "title": "Breath of the Wild Main Theme", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: Breath of the Wild", "context": "Audio - The Legend of Z<PERSON>a: Breath of the Wild", "audioUrl": "/audio/zelda/breath-of-the-wild/1-02. Main Theme.mp3", "year": 2017, "genre": "Adventure Game Soundtrack", "console": "Nintendo Switch"}, {"id": 637, "title": "Hyrule Castle", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: Breath of the Wild", "context": "Audio - The Legend of Z<PERSON>a: Breath of the Wild", "audioUrl": "/audio/zelda/breath-of-the-wild/6-18. Hyrule Castle.mp3", "year": 2017, "genre": "Adventure Game Soundtrack", "console": "Nintendo Switch"}, {"id": 638, "title": "<PERSON><PERSON>'s Theme", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: Breath of the Wild", "context": "Audio - The Legend of Z<PERSON>a: Breath of the Wild", "audioUrl": "/audio/zelda/breath-of-the-wild/7-09. <PERSON><PERSON>'s Theme (Song of the Hero ver.).mp3", "year": 2017, "genre": "Adventure Game Soundtrack", "console": "Nintendo Switch"}, {"id": 639, "title": "Title Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON><PERSON>: <PERSON><PERSON>'s Mask", "context": "Audio - The Legend of <PERSON><PERSON>a: <PERSON><PERSON>'s Mask", "audioUrl": "/audio/zelda/majora-mask/1-01. Title Theme.mp3", "year": 2000, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 640, "title": "Clock Town, First Day", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON><PERSON>: <PERSON><PERSON>'s Mask", "context": "Audio - The Legend of <PERSON><PERSON>a: <PERSON><PERSON>'s Mask", "audioUrl": "/audio/zelda/majora-mask/1-08. Clock Town, First Day.mp3", "year": 2000, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 641, "title": "Astral Observatory", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON><PERSON>: <PERSON><PERSON>'s Mask", "context": "Audio - The Legend of <PERSON><PERSON>a: <PERSON><PERSON>'s Mask", "audioUrl": "/audio/zelda/majora-mask/1-25. Astral Observatory.mp3", "year": 2000, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 642, "title": "Song of Healing", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON><PERSON>: <PERSON><PERSON>'s Mask", "context": "Audio - The Legend of <PERSON><PERSON>a: <PERSON><PERSON>'s Mask", "audioUrl": "/audio/zelda/majora-mask/1-29. Song of Healing.mp3", "year": 2000, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 643, "title": "Deku Palace", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON><PERSON>: <PERSON><PERSON>'s Mask", "context": "Audio - The Legend of <PERSON><PERSON>a: <PERSON><PERSON>'s Mask", "audioUrl": "/audio/zelda/majora-mask/1-45. Deku Palace.mp3", "year": 2000, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 644, "title": "Woodfall Temple", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON><PERSON>: <PERSON><PERSON>'s Mask", "context": "Audio - The Legend of <PERSON><PERSON>a: <PERSON><PERSON>'s Mask", "audioUrl": "/audio/zelda/majora-mask/1-51. Woodfall Temple.mp3", "year": 2000, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 645, "title": "The End Credits", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON><PERSON>: <PERSON><PERSON>'s Mask", "context": "Audio - The Legend of <PERSON><PERSON>a: <PERSON><PERSON>'s Mask", "audioUrl": "/audio/zelda/majora-mask/2-53. The End-Credits.mp3", "year": 2000, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 646, "title": "Title Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/01 Title Theme.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 647, "title": "De<PERSON> Tree", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/03 Deku Tree.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 648, "title": "Kokiri Forest", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/06 Kokiri Forest.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 649, "title": "Hyrule Field Main Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/19a Hyrule Field Main Theme.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 650, "title": "Hyrule Castle Courtyard", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/23 Hyrule Castle Courtyard.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 651, "title": "<PERSON><PERSON><PERSON>'s Theme", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/26 <PERSON><PERSON><PERSON>'s Theme.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 652, "title": "Lon Lon Ranch", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/28 Lon Lon Ranch.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 653, "title": "Kakariko Village", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/30 Kakariko Village.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 654, "title": "<PERSON><PERSON>'s Song", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/34 Ocarina ~<PERSON><PERSON>'s Song~.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 655, "title": "Lost Woods", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/35 Lost Woods.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 656, "title": "Great Fairy's Fountain", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/40 Great Fairy's Fountain.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 657, "title": "Master Sword", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/46 Master Sword.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 658, "title": "Song of Storms", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/56 Ocarina ~Song of Storms~.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 659, "title": "Gerudo Valley", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: <PERSON><PERSON><PERSON> of Time", "context": "Audio - The Legend of Zelda: <PERSON><PERSON><PERSON> of Time", "audioUrl": "/audio/zelda/ocarina-of-time/68 Gerudo Valley.mp3", "year": 1998, "genre": "Adventure Game Soundtrack", "console": "Nintendo 64"}, {"id": 660, "title": "Ballad of the Goddess", "artist": "<PERSON><PERSON><PERSON>", "composer": "<PERSON><PERSON><PERSON>", "game": "The Legend of Zelda: Skyward Sword", "context": "Audio - The Legend of Zelda: Skyward Sword", "audioUrl": "/audio/zelda/skyward-sword/1-04 Theme of Skyward Sword - Ballad of the Goddess.mp3", "year": 2011, "genre": "Adventure Game Soundtrack", "console": "Wii"}, {"id": 662, "title": "Title", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: Twilight Princess", "context": "Audio - The Legend of Zelda: Twilight Princess", "audioUrl": "/audio/zelda/twilight-princess/1-02 Title.mp3", "year": 2006, "genre": "Adventure Game Soundtrack", "console": "GameCube/Wii"}, {"id": 663, "title": "Ordon Ranch", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of <PERSON><PERSON>a: Twilight Princess", "context": "Audio - The Legend of Zelda: Twilight Princess", "audioUrl": "/audio/zelda/twilight-princess/1-09 Ordon Ranch.mp3", "year": 2006, "genre": "Adventure Game Soundtrack", "console": "GameCube/Wii"}, {"id": 664, "title": "Title", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of Zelda: The Wind Waker", "context": "Audio - The Legend of Zelda: The Wind Waker", "audioUrl": "/audio/zelda/wind-waker/1-01. Title.mp3", "year": 2002, "genre": "Adventure Game Soundtrack", "console": "GameCube"}, {"id": 665, "title": "Outset Island", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of Zelda: The Wind Waker", "context": "Audio - The Legend of Zelda: The Wind Waker", "audioUrl": "/audio/zelda/wind-waker/1-04. Outset Island.mp3", "year": 2002, "genre": "Adventure Game Soundtrack", "console": "GameCube"}, {"id": 666, "title": "Beedle's Shop", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of Zelda: The Wind Waker", "context": "Audio - The Legend of Zelda: The Wind Waker", "audioUrl": "/audio/zelda/wind-waker/1-08. Beedle's Shop.mp3", "year": 2002, "genre": "Adventure Game Soundtrack", "console": "GameCube"}, {"id": 667, "title": "Windfall Island", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of Zelda: The Wind Waker", "context": "Audio - The Legend of Zelda: The Wind Waker", "audioUrl": "/audio/zelda/wind-waker/1-28. Windfall Island.mp3", "year": 2002, "genre": "Adventure Game Soundtrack", "console": "GameCube"}, {"id": 668, "title": "Dragon Roost Island", "artist": "<PERSON><PERSON>", "composer": "<PERSON><PERSON>", "game": "The Legend of Zelda: The Wind Waker", "context": "Audio - The Legend of Zelda: The Wind Waker", "audioUrl": "/audio/zelda/wind-waker/1-38. Dragon Roost Island.mp3", "year": 2002, "genre": "Adventure Game Soundtrack", "console": "GameCube"}]